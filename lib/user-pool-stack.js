const { Stack } = require('aws-cdk-lib');
const cognito = require('aws-cdk-lib/aws-cognito');

class UserPoolStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // Create User Pool
    const userPool = new cognito.UserPool(this, 'JourneyUserPool', {
      userPoolName: 'journey-user-pool',
      selfSignUpEnabled: false, // We'll handle sign-up through our API
      signInAliases: {
        username: true,
        email: true,
        phone: true,
      },
      standardAttributes: {
        email: {
          required: true,
          mutable: true,
        },
        phoneNumber: {
          required: false,
          mutable: true,
        },
      },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireDigits: true,
        requireSymbols: true,
      },
    });

    // Create User Pool Client
    const userPoolClient = new cognito.UserPoolClient(this, 'JourneyUserPoolClient', {
      userPool,
      authFlows: {
        adminUserPassword: true,  // Enable admin-user-password auth flow
        userPassword: true,       // Enable user-password auth flow
      },
      generateSecret: false,      // No client secret needed for public clients
    });

    // Export the user pool, client
    this.userPool = userPool;
    this.userPoolClient = userPoolClient;
  }
}

module.exports = { UserPoolStack };
