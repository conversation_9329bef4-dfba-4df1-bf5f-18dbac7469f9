const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const iam = require('aws-cdk-lib/aws-iam');
const { DynamoEventSource } = require('aws-cdk-lib/aws-lambda-event-sources');
const s3 = require('aws-cdk-lib/aws-s3');
const { S3EventSource } = require('aws-cdk-lib/aws-lambda-event-sources');
const dynamodb = require('aws-cdk-lib/aws-dynamodb');

class SignUpStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // Get references to DynamoDB tables and Cognito User Pool from props
    const {
      userPool,
      userPoolClient,
      userTable,
    } = props;

    // Databases
    const emailVerificationTable = new dynamodb.Table(this, 'EmailVerificationTable', {
        tableName: 'email_verification_table',
        partitionKey: { name: 'email', type: dynamodb.AttributeType.STRING },
        sortKey: { name: 'verificationCode', type: dynamodb.AttributeType.STRING },
        billingMode: dynamodb.BillingMode.PROVISIONED,
        readCapacity: 1,
        writeCapacity: 1,
        stream: dynamodb.StreamViewType.NEW_IMAGE, // Enable streams with new image
    //   removalPolicy: props?.isProd ? undefined : RemovalPolicy.DESTROY,
    });

    // Phone Number Verification Table
    const phoneNumberVerificationTable = new dynamodb.Table(this, 'PhoneNumberVerificationTable', {
        tableName: 'phone_number_verification_table',
        partitionKey: { name: 'phoneNumber', type: dynamodb.AttributeType.STRING },
        sortKey: { name: 'verificationCode', type: dynamodb.AttributeType.STRING },
        billingMode: dynamodb.BillingMode.PROVISIONED,
        readCapacity: 1,
        writeCapacity: 1,
    //   removalPolicy: props?.isProd ? undefined : dynamodb.RemovalPolicy.DESTROY,
    });

    // User Sign Up Temp Table
    const userSignUpTempTable = new dynamodb.Table(this, 'UserSignUpTempTable', {
        tableName: 'user_sign_up_temp',
        partitionKey: { name: 'sessionToken', type: dynamodb.AttributeType.STRING },
        billingMode: dynamodb.BillingMode.PROVISIONED,
        readCapacity: 1,
        writeCapacity: 1,
    //   removalPolicy: props?.isProd ? undefined : dynamodb.RemovalPolicy.DESTROY,
    });

    // Add GSI to User Sign Up Temp Table
    userSignUpTempTable.addGlobalSecondaryIndex({
        indexName: 'username-index',
        partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
        projectionType: dynamodb.ProjectionType.INCLUDE,
        nonKeyAttributes: ['username'],
    });

    // Common Lambda configuration
    const commonLambdaProps = {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'index.handler',
      timeout: Duration.seconds(30),
    };

    // Create Lambda functions with their permissions
    const verifyUsername = new lambda.Function(this, 'VerifyUsername', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/verify-username/function.zip'),
    });

    const verifyPhone = new lambda.Function(this, 'VerifyPhone', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/verify-phone/function.zip'),
    });

    const verifyEmail = new lambda.Function(this, 'VerifyEmail', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/verify-email/function.zip'),
    });

    const sendVerificationEmail = new lambda.Function(this, 'SendVerificationEmail', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/send-verification-email/function.zip'),
    });

    // const sendVerificationSMS = new lambda.Function(this, 'SendVerificationSMS', {
    //   ...commonLambdaProps,
    //   code: lambda.Code.fromAsset('lambda/send-verification-sms/function.zip'),
    // });

    const validateEmailVerificationCode = new lambda.Function(this, 'ValidateEmailVerificationCode', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/validate-email-verification-code/function.zip'),
    });

    const validateSMSVerificationCode = new lambda.Function(this, 'ValidateSMSVerificationCode', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/validate-sms-verification-code/function.zip'),
    });

    const removeEmail = new lambda.Function(this, 'RemoveEmail', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/remove-email/function.zip'),
    });

    const removePhoneNumber = new lambda.Function(this, 'RemovePhoneNumber', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/remove-phone-number/function.zip'),
    });

    const signUp = new lambda.Function(this, 'SignUp', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/sign-up/function.zip'),
      environment: {
        USER_POOL_ID: userPool.userPoolId,
        CLIENT_ID: userPoolClient.userPoolClientId,
      },
    });

    const destroySession = new lambda.Function(this, 'DestroySession', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/destroy-session/function.zip'),
    });

    // Grant permissions to Lambda functions
    
    // verifyUsername permissions
    userTable.grantReadWriteData(verifyUsername);
    userSignUpTempTable.grantReadWriteData(verifyUsername);

    // verifyPhone permissions
    userTable.grantReadWriteData(verifyPhone);
    userSignUpTempTable.grantReadWriteData(verifyPhone);
    phoneNumberVerificationTable.grantReadWriteData(verifyPhone);

    // verifyEmail permissions
    userTable.grantReadWriteData(verifyEmail);
    userSignUpTempTable.grantReadWriteData(verifyEmail);
    emailVerificationTable.grantReadWriteData(verifyEmail);

    // sendVerificationEmail permissions
    emailVerificationTable.grantReadWriteData(sendVerificationEmail);

    // sendVerificationSMS permissions
    // phoneNumberVerificationTable.grantReadWriteData(sendVerificationSMS);

    // validateEmailVerificationCode permissions
    userSignUpTempTable.grantReadWriteData(validateEmailVerificationCode);
    emailVerificationTable.grantReadWriteData(validateEmailVerificationCode);

    // validateSMSVerificationCode permissions
    userSignUpTempTable.grantReadWriteData(validateSMSVerificationCode);
    phoneNumberVerificationTable.grantReadWriteData(validateSMSVerificationCode);

    // removeEmail permissions
    userSignUpTempTable.grantReadWriteData(removeEmail);

    // removePhoneNumber permissions
    userSignUpTempTable.grantReadWriteData(removePhoneNumber);

    // Grant SES permissions to the Lambda
    const sesStatement = new iam.PolicyStatement({
      actions: ['ses:SendEmail', 'ses:SendRawEmail'],
      resources: ['*'], // You might want to restrict this to specific email addresses
    });
    sendVerificationEmail.addToRolePolicy(sesStatement);

    // Add DynamoDB Stream trigger
    sendVerificationEmail.addEventSource(new DynamoEventSource(emailVerificationTable, {
      startingPosition: lambda.StartingPosition.LATEST,
      batchSize: 1,
      retryAttempts: 1,
    }));

    // Grant permissions for the signUp function
    userPool.grant(signUp, 
      'cognito-idp:AdminCreateUser', 
      'cognito-idp:AdminSetUserPassword',
      'cognito-idp:AdminInitiateAuth'
    );

    userSignUpTempTable.grantReadWriteData(signUp);

    userTable.grantReadWriteData(signUp);

    // Grant permissions to Lambda function
    userSignUpTempTable.grantWriteData(destroySession);

    // Export Lambda functions as public properties
    this.verifyUsername = verifyUsername;
    this.verifyPhone = verifyPhone;
    this.verifyEmail = verifyEmail;
    this.validateEmailVerificationCode = validateEmailVerificationCode;
    this.validateSMSVerificationCode = validateSMSVerificationCode;
    this.removeEmail = removeEmail;
    this.removePhoneNumber = removePhoneNumber;
    this.signUp = signUp;
    this.destroySession = destroySession;
  }
}

module.exports = { SignUpStack };
