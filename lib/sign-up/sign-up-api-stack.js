
const { Stack } = require('aws-cdk-lib');
const apigateway = require('aws-cdk-lib/aws-apigateway');;

class SignUpAPIStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // Extract Lambda functions from props
    const {
      verifyUsername,
      verifyPhone,
      verifyEmail,
      validateEmailVerificationCode,
      validateSMSVerificationCode,
      removeEmail,
      removePhoneNumber,
      destroySession,
      signUp,
    } = props;

    // Create REST API
    const api = new apigateway.RestApi(this, 'JourneyAuthAPI', {
      restApiName: 'Journey Auth API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_ORIGINS,
      },
    });

    // Create models for request validation
    const usernameModel = api.addModel('UsernameModel', {
      contentType: 'application/json',
      modelName: 'UsernameModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['username'],  // sessionToken is not required
        properties: {
          username: { type: apigateway.JsonSchemaType.STRING },
          sessionToken: { type: apigateway.JsonSchemaType.STRING }  // optional parameter
        },
      },
    });

    const phoneModel = api.addModel('PhoneModel', {
      contentType: 'application/json',
      modelName: 'PhoneModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['phoneNumber', 'countryCode', 'sessionToken'],
        properties: {
          phoneNumber: { type: apigateway.JsonSchemaType.STRING },
          countryCode: { type: apigateway.JsonSchemaType.STRING },
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    const emailModel = api.addModel('EmailModel', {
      contentType: 'application/json',
      modelName: 'EmailModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['email', 'sessionToken'],
        properties: {
          email: { type: apigateway.JsonSchemaType.STRING },
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    const emailVerificationModel = api.addModel('EmailVerificationModel', {
      contentType: 'application/json',
      modelName: 'EmailVerificationModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['email', 'sessionToken', 'verificationCode'],
        properties: {
          email: { type: apigateway.JsonSchemaType.STRING },
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
          verificationCode: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    const removeEmailModel = api.addModel('RemoveEmailModel', {
      contentType: 'application/json',
      modelName: 'RemoveEmailModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['sessionToken'],
        properties: {
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    const smsVerificationModel = api.addModel('SMSVerificationModel', {
      contentType: 'application/json',
      modelName: 'SMSVerificationModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['phoneNumber', 'countryCode', 'sessionToken', 'verificationCode'],
        properties: {
          phoneNumber: { type: apigateway.JsonSchemaType.STRING },
          countryCode: { type: apigateway.JsonSchemaType.STRING },
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
          verificationCode: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    const removePhoneModel = api.addModel('RemovePhoneModel', {
      contentType: 'application/json',
      modelName: 'RemovePhoneModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['sessionToken'],
        properties: {
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    const signUpModel = api.addModel('SignUpModel', {
      contentType: 'application/json',
      modelName: 'SignUpModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['sessionToken', 'password'],
        properties: {
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
          password: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    // Add the destroy session model
    const destroySessionModel = api.addModel('DestroySessionModel', {
      contentType: 'application/json',
      modelName: 'DestroySessionModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['sessionToken'],
        properties: {
          sessionToken: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    // Helper function to create POST method with validation
    const createPostMethod = (resource, lambda, model) => {
      resource.addMethod('POST', new apigateway.LambdaIntegration(lambda), {
        requestValidator: new apigateway.RequestValidator(this, `${resource.path}Validator`, {
          restApi: api,
          validateRequestBody: true,
          validateRequestParameters: false,
        }),
        requestModels: {
          'application/json': model,
        },
      });
    };

    // Create API resources and methods
    const verifyResource = api.root.addResource('verify');
    const validateResource = api.root.addResource('validate');
    const removeResource = api.root.addResource('remove');

    // /verify/username
    const verifyUsernameResource = verifyResource.addResource('username');
    createPostMethod(verifyUsernameResource, verifyUsername, usernameModel);

    // /verify/phone
    const verifyPhoneResource = verifyResource.addResource('phone');
    createPostMethod(verifyPhoneResource, verifyPhone, phoneModel);

    // /verify/email
    const verifyEmailResource = verifyResource.addResource('email');
    createPostMethod(verifyEmailResource, verifyEmail, emailModel);

    // /validate/email/verification/code
    const validateEmailResource = validateResource.addResource('email')
      .addResource('verification').addResource('code');
    createPostMethod(validateEmailResource, validateEmailVerificationCode, emailVerificationModel);

    // /validate/sms/verification/code
    const validateSMSResource = validateResource.addResource('sms')
      .addResource('verification').addResource('code');
    createPostMethod(validateSMSResource, validateSMSVerificationCode, smsVerificationModel);

    // /remove/email
    const removeEmailResource = removeResource.addResource('email');
    createPostMethod(removeEmailResource, removeEmail, removeEmailModel);

    // /remove/phone
    const removePhoneResource = removeResource.addResource('phone');
    createPostMethod(removePhoneResource, removePhoneNumber, removePhoneModel);

    const signUpResource = api.root.addResource('signup');
    createPostMethod(signUpResource, signUp, signUpModel);

    // Create destroy-session endpoint
    const destroySessionResource = api.root.addResource('destroy-session');
    createPostMethod(destroySessionResource, destroySession, destroySessionModel);
  }
}

module.exports = { SignUpAPIStack };
