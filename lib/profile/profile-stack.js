const { Stack, Duration, RemovalPolicy } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const s3 = require('aws-cdk-lib/aws-s3');
const s3n = require('aws-cdk-lib/aws-s3-notifications');
const apigateway = require('aws-cdk-lib/aws-apigateway');
const iam = require('aws-cdk-lib/aws-iam');

class ProfileStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    const {
      userTable,
      userPool,
      isProd,
    } = props;

    // Common Lambda configuration
    const commonLambdaProps = {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
    };

    // Create Lambda for updating user profile
    const updateUserProfile = new lambda.Function(this, 'UpdateUserProfile', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/update-user-profile/function.zip'),
      environment: {
        USER_TABLE_NAME: userTable.tableName,
      },
    });

    // Grant permissions to the Lambda
    userTable.grantWriteData(updateUserProfile);

    // Create API Gateway with Cognito Authorizer
    const api = new apigateway.RestApi(this, 'ProfileAPI', {
      restApiName: 'Profile API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [...apigateway.Cors.DEFAULT_HEADERS, 'Authorization', 'Content-Type'],
      },
      binaryMediaTypes: ['image/jpeg', 'image/png'], // Add support for binary content
    });

    // Create Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'ProfileAuthorizer', {
      cognitoUserPools: [userPool],
    });

    // Create request model
    const updateProfileModel = api.addModel('UpdateProfileModel', {
      contentType: 'application/json',
      modelName: 'UpdateProfileModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['firstName', 'lastName', 'bio', 'currentLocation'],
        properties: {
          firstName: { 
            type: apigateway.JsonSchemaType.STRING,
            maxLength: 100 
          },
          lastName: { 
            type: apigateway.JsonSchemaType.STRING,
            maxLength: 100 
          },
          bio: { 
            type: apigateway.JsonSchemaType.STRING,
            maxLength: 250 
          },
          currentLocation: { 
            type: apigateway.JsonSchemaType.STRING,
            maxLength: 100 
          },
        },
      },
    });

    // Create profile resource and method
    const profileResource = api.root.addResource('profile');
    profileResource.addMethod('POST', new apigateway.LambdaIntegration(updateUserProfile), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestValidator: new apigateway.RequestValidator(this, 'UpdateProfileValidator', {
        restApi: api,
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      requestModels: {
        'application/json': updateProfileModel,
      },
    });

    // Create Lambda for processing profile picture uploads
    const processProfilePicture = new lambda.Function(this, 'ProcessProfilePicture', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/process-profile-picture/function.zip'),
      environment: {
        USER_TABLE_NAME: userTable.tableName,
      },
    });

    // Create Lambda for processing banner picture uploads
    const processBannerPicture = new lambda.Function(this, 'ProcessBannerPicture', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/process-banner-picture/function.zip'),
      environment: {
        USER_TABLE_NAME: userTable.tableName,
      },
    });

    const profilePicturesBucket = new s3.Bucket(this, 'ProfilePicturesBucket', {
      bucketName: `journey-profile-pictures`,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.PUT,
            s3.HttpMethods.POST,
            s3.HttpMethods.GET,
          ],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
      removalPolicy: isProd ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      autoDeleteObjects: !isProd,
    });

    // Create S3 bucket for profile banner pictures
    const profileBannerPicturesBucket = new s3.Bucket(this, 'ProfileBannerPicturesBucket', {
      bucketName: `journey-profile-banner-pictures`,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.PUT,
            s3.HttpMethods.POST,
            s3.HttpMethods.GET,
          ],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
      removalPolicy: isProd ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      autoDeleteObjects: !isProd,
    });

    // Export bucket as public property
    // this.profileBannerPicturesBucket = profileBannerPicturesBucket;

    // Add S3 event notification to trigger the Lambda when objects are created
    profilePicturesBucket.addEventNotification(
      s3.EventType.OBJECT_CREATED, 
      new s3n.LambdaDestination(processProfilePicture),
      //{ prefix: 'standard/' }  // Only trigger for objects in the standard/ prefix
    );

    // Grant permissions without creating circular dependencies
    userTable.grantWriteData(processProfilePicture);

    // Add S3 event notification to trigger the Lambda when objects are created
    profileBannerPicturesBucket.addEventNotification(
      s3.EventType.OBJECT_CREATED, 
      new s3n.LambdaDestination(processBannerPicture),
      //{ prefix: 'standard/' }  // Only trigger for objects in the standard/ prefix
    );

    // Grant permissions without creating circular dependencies
    userTable.grantWriteData(processBannerPicture);

    // Create Lambda for uploading profile picture
    const uploadProfilePicture = new lambda.Function(this, 'UploadProfilePicture', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/upload-profile-picture/function.zip'),
      memorySize: 1024, // Increase memory for image processing
      timeout: Duration.seconds(60), // Increase timeout for image processing
      environment: {
        BUCKET_NAME: profilePicturesBucket.bucketName,
      },
    });

    // Create Lambda for uploading banner picture
    const uploadBannerPicture = new lambda.Function(this, 'UploadBannerPicture', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/upload-banner-picture/function.zip'),
      memorySize: 1024, // Increase memory for image processing
      timeout: Duration.seconds(60), // Increase timeout for image processing
      environment: {
        BUCKET_NAME: profileBannerPicturesBucket.bucketName,
      },
    });

    // Grant S3 permissions to the Lambda
    profilePicturesBucket.grantReadWrite(uploadProfilePicture);
    profileBannerPicturesBucket.grantReadWrite(uploadBannerPicture);

    // Create profile picture upload resource
    const pictureResource = api.root.addResource('profile-picture');
    pictureResource.addMethod('POST', new apigateway.LambdaIntegration(uploadProfilePicture), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
    });

    const bannerResource = api.root.addResource('banner-picture');
    bannerResource.addMethod('POST', new apigateway.LambdaIntegration(uploadBannerPicture), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
    });

    // Create Lambda for getting user profile (moved from ProfileStack)
    const getUserProfile = new lambda.Function(this, 'GetUserProfile', {
      ...commonLambdaProps,
      code: lambda.Code.fromAsset('lambda/get-user-profile/function.zip'),
      environment: {
        USER_TABLE_NAME: userTable.tableName,
        PROFILE_PICTURES_BUCKET: profilePicturesBucket.bucketName,
        PROFILE_BANNER_PICTURES_BUCKET: profileBannerPicturesBucket.bucketName,
      },
    });

    // Grant permissions to the Lambda
    userTable.grantReadData(getUserProfile);
    
    // Grant permission to generate presigned URLs
    profilePicturesBucket.grantRead(getUserProfile);

    profileBannerPicturesBucket.grantRead(getUserProfile);

    // Add GET method to profile resource
    profileResource.addMethod('GET', new apigateway.LambdaIntegration(getUserProfile), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    // Export Lambda functions as public properties
    // this.processProfilePicture = processProfilePicture;
    // this.processBannerPicture = processBannerPicture;
    // this.getUserProfile = getUserProfile;

    //Export bucket as public property
    this.profilePicturesBucket = profilePicturesBucket;
  }
}

module.exports = { ProfileStack };
