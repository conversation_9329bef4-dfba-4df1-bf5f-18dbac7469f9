const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const apigateway = require('aws-cdk-lib/aws-apigateway');
const s3 = require('aws-cdk-lib/aws-s3');
const iam = require('aws-cdk-lib/aws-iam');
const dynamodb = require('aws-cdk-lib/aws-dynamodb');

class CreateJourneyStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    const {
      userPool,
      userTable,
      profilePicturesBucket,
      isProd,
     } = props;

    // Create DynamoDB table for journeys
    const journeyTable = new dynamodb.Table(this, 'JourneyTable', {
      tableName: `journey-${this.account}-${this.region}`,
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'date_created', type: dynamodb.AttributeType.NUMBER },
      billingMode: dynamodb.BillingMode.PROVISIONED,
      readCapacity: 1,
      writeCapacity: 1,
      // removalPolicy: isProd ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    const journeyCoverPhotosBucket = new s3.Bucket(this, 'JourneyCoverPhotosBucket', {
      bucketName: `journey-cover-photos-${this.account}-${this.region}`,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.PUT,
            s3.HttpMethods.POST,
            s3.HttpMethods.GET,
          ],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
    });

    // Create Lambda function to generate presigned URL
    const generatePresignedUrlLambda = new lambda.Function(this, 'GeneratePresignedUrl', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/create-journey-cover-photo-url/function.zip'),
      environment: {
        JOURNEY_COVER_PHOTOS_BUCKET: journeyCoverPhotosBucket.bucketName,
      },
    });

    // Grant Lambda permission to generate presigned URLs for the bucket
    const s3PutPolicy = new iam.PolicyStatement({
      actions: ['s3:PutObject'],
      resources: [journeyCoverPhotosBucket.arnForObjects('*')],
    });

    generatePresignedUrlLambda.addToRolePolicy(s3PutPolicy);

    journeyCoverPhotosBucket.grantReadWrite(generatePresignedUrlLambda);

    // Create Lambda function to create a journey
    const createJourneyLambda = new lambda.Function(this, 'CreateJourney', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/create-journey/function.zip'),
      environment: {
        JOURNEY_TABLE_NAME: journeyTable.tableName,
        JOURNEY_COVER_PHOTOS_BUCKET: journeyCoverPhotosBucket.bucketName,
        USER_TABLE_NAME: userTable.tableName,
      },
    });

    // Grant Lambda permission to write to the journey table
    journeyTable.grantWriteData(createJourneyLambda);

    // Grant Lambda permission to read from the user table
    userTable.grantReadData(createJourneyLambda);

    journeyCoverPhotosBucket.grantReadWrite(createJourneyLambda);

    // Create Lambda function to link posts and thoughts to journey
    const linkToJourneyLambda = new lambda.Function(this, 'LinkToJourney', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/link-to-journey/function.zip'),
      environment: {
        JOURNEY_TABLE_NAME: journeyTable.tableName,
      },
    });

    // Grant Lambda permission to update the journey table
    journeyTable.grantWriteData(linkToJourneyLambda);

    // Create API Gateway with Cognito Authorizer
    const api = new apigateway.RestApi(this, 'JourneyAPI', {
      restApiName: 'Journey API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [...apigateway.Cors.DEFAULT_HEADERS, 'Authorization', 'Content-Type'],
      },
    });

    // Create Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'JourneyAuthorizer', {
      cognitoUserPools: [userPool],
    });

    // Create journey resource and method
    const journeyResource = api.root.addResource('journey');
    
    // Add POST method to create a journey
    journeyResource.addMethod('POST', new apigateway.LambdaIntegration(createJourneyLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    const uploadUrlResource = api.root.addResource('upload-url');

    // POST method to accept request body
    uploadUrlResource.addMethod('POST', new apigateway.LambdaIntegration(generatePresignedUrlLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    // Create link-to-journey resource and method
    const linkToJourneyResource = api.root.addResource('link-to-journey');

    // POST method to link posts and thoughts to journey
    linkToJourneyResource.addMethod('POST', new apigateway.LambdaIntegration(linkToJourneyLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    // Create Lambda function to get journeys by user
    const getJourneysByUserLambda = new lambda.Function(this, 'GetJourneysByUser', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/get-journeys-by-user/function.zip'),
      environment: {
        JOURNEY_TABLE_NAME: journeyTable.tableName,
        USER_TABLE_NAME: userTable.tableName,
        JOURNEY_COVER_PHOTOS_BUCKET: journeyCoverPhotosBucket.bucketName,
        PROFILE_PICTURES_BUCKET: profilePicturesBucket.bucketName,
      },
    });

    // Grant Lambda permission to read from the journey table
    journeyTable.grantReadData(getJourneysByUserLambda);

    // Grant Lambda permission to read from the user table
    userTable.grantReadData(getJourneysByUserLambda);

    // Grant Lambda permission to read from the profile pictures bucket
    profilePicturesBucket.grantRead(getJourneysByUserLambda);

    // Grant Lambda permission to read from the journey cover photos bucket
    journeyCoverPhotosBucket.grantRead(getJourneysByUserLambda);

    // Create Lambda function to add posts and thoughts to journey
    const addPostsAndThoughtsToJourneyLambda = new lambda.Function(this, 'AddPostsAndThoughtsToJourney', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/add-posts-and-thoughts-to-journey/function.zip'),
      environment: {
        JOURNEY_TABLE_NAME: journeyTable.tableName,
      },
    });

    // Grant Lambda permission to update the journey table
    journeyTable.grantWriteData(addPostsAndThoughtsToJourneyLambda);

    // Create API Gateway model for add posts and thoughts request
    const addPostsAndThoughtsModel = api.addModel('AddPostsAndThoughtsModel', {
      contentType: 'application/json',
      modelName: 'AddPostsAndThoughtsModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['journeyId'],
        properties: {
          journeyId: { type: apigateway.JsonSchemaType.INTEGER },
          postIds: {
            type: apigateway.JsonSchemaType.ARRAY,
            items: { type: apigateway.JsonSchemaType.INTEGER }
          },
          thoughtIds: {
            type: apigateway.JsonSchemaType.ARRAY,
            items: { type: apigateway.JsonSchemaType.INTEGER }
          },
        },
      },
    });

    // GET method to get journeys with optional username, startDate, and endDate query parameters
    journeyResource.addMethod('GET', new apigateway.LambdaIntegration(getJourneysByUserLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    // PATCH method to add posts and thoughts to journey
    journeyResource.addMethod('PATCH', new apigateway.LambdaIntegration(addPostsAndThoughtsToJourneyLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestValidator: new apigateway.RequestValidator(this, 'AddPostsAndThoughtsValidator', {
        restApi: api,
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      requestModels: {
        'application/json': addPostsAndThoughtsModel,
      },
    });

    // Export resources as public properties
    this.journeyTable = journeyTable;
    this.journeyCoverPhotosBucket = journeyCoverPhotosBucket;
    this.journeyApi = api;
  }

}

module.exports = { CreateJourneyStack };
