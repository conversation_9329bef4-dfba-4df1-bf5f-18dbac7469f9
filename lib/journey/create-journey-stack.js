const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const apigateway = require('aws-cdk-lib/aws-apigateway');
const s3 = require('aws-cdk-lib/aws-s3');
const iam = require('aws-cdk-lib/aws-iam');
const dynamodb = require('aws-cdk-lib/aws-dynamodb');

class CreateJourneyStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    const { 
      userPool, 
      isProd,
     } = props;

    // Create DynamoDB table for journeys
    const journeyTable = new dynamodb.Table(this, 'JourneyTable', {
      tableName: 'journey',
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'date_created', type: dynamodb.AttributeType.NUMBER },
      billingMode: dynamodb.BillingMode.PROVISIONED,
      readCapacity: 1,
      writeCapacity: 1,
      // removalPolicy: isProd ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    const journeyCoverPhotosBucket = new s3.Bucket(this, 'JourneyCoverPhotosBucket', {
      bucketName: `journey-cover-photos-${this.account}-${this.region}`,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.PUT,
            s3.HttpMethods.POST,
            s3.HttpMethods.GET,
          ],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
    });

    // Create Lambda function to generate presigned URL
    const generatePresignedUrlLambda = new lambda.Function(this, 'GeneratePresignedUrl', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/generate-presigned-url/function.zip'),
      environment: {
        JOURNEY_COVER_PHOTOS_BUCKET: journeyCoverPhotosBucket.bucketName,
      },
    });

    // Grant Lambda permission to generate presigned URLs for the bucket
    const s3PutPolicy = new iam.PolicyStatement({
      actions: ['s3:PutObject'],
      resources: [journeyCoverPhotosBucket.arnForObjects('*')],
    });

    generatePresignedUrlLambda.addToRolePolicy(s3PutPolicy);

    journeyCoverPhotosBucket.grantReadWrite(generatePresignedUrlLambda);

    // Create Lambda function to create a journey
    const createJourneyLambda = new lambda.Function(this, 'CreateJourney', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/create-journey/function.zip'),
      environment: {
        JOURNEY_TABLE_NAME: journeyTable.tableName,
        JOURNEY_COVER_PHOTOS_BUCKET: journeyCoverPhotosBucket.bucketName,
      },
    });

    // Grant Lambda permission to write to the journey table
    journeyTable.grantWriteData(createJourneyLambda);

    // Create API Gateway with Cognito Authorizer
    const api = new apigateway.RestApi(this, 'JourneyAPI', {
      restApiName: 'Journey API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [...apigateway.Cors.DEFAULT_HEADERS, 'Authorization', 'Content-Type'],
      },
    });

    // Create Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'JourneyAuthorizer', {
      cognitoUserPools: [userPool],
    });

    // Create journey resource and method
    const journeyResource = api.root.addResource('journey');
    
    // Add POST method to create a journey
    journeyResource.addMethod('POST', new apigateway.LambdaIntegration(createJourneyLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    // Export resources as public properties
    this.journeyTable = journeyTable;
    this.journeyCoverPhotosBucket = journeyCoverPhotosBucket;
    this.journeyApi = api;
  }

}

module.exports = { CreateJourneyStack };
