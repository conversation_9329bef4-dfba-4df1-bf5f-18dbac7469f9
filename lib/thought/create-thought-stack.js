const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const apigateway = require('aws-cdk-lib/aws-apigateway');
const dynamodb = require('aws-cdk-lib/aws-dynamodb');

class CreateThoughtStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    const { 
      userPool, 
      isProd,
     } = props;

    // Create DynamoDB table for thoughts
    const thoughtTable = new dynamodb.Table(this, 'ThoughtTable', {
      tableName: `thought-${this.account}-${this.region}`,
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'date_created', type: dynamodb.AttributeType.NUMBER },
      billingMode: dynamodb.BillingMode.PROVISIONED,
      readCapacity: 1,
      writeCapacity: 1,
      // removalPolicy: isProd ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    // Create Lambda function to create a thought
    const createThoughtLambda = new lambda.Function(this, 'CreateThought', {
        runtime: lambda.Runtime.PROVIDED_AL2023,
        handler: 'bootstrap',
        timeout: Duration.seconds(30),
        code: lambda.Code.fromAsset('lambda/create-thought/function.zip'),
        environment: {
            THOUGHT_TABLE_NAME: thoughtTable.tableName,
        },
    });

    // Grant Lambda permission to write to the thought table
    thoughtTable.grantWriteData(createThoughtLambda);

    // Create API Gateway with Cognito Authorizer
    const api = new apigateway.RestApi(this, 'ThoughtAPI', {
      restApiName: 'Thought API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [...apigateway.Cors.DEFAULT_HEADERS, 'Authorization', 'Content-Type'],
      },
    });

    // Create Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'ThoughtAuthorizer', {
      cognitoUserPools: [userPool],
    });

    // Create API Gateway model for thought request
    const thoughtRequestModel = api.addModel('ThoughtRequestModel', {
      contentType: 'application/json',
      modelName: 'ThoughtRequestModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['thoughtBody'],
        properties: {
          thoughtBody: {
            type: apigateway.JsonSchemaType.STRING,
            maxLength: 250,
            description: 'The content of the thought (max 250 characters)'
          },
          journey: {
            type: apigateway.JsonSchemaType.INTEGER,
            description: 'Optional journey ID to associate with the thought'
          }
        },
        additionalProperties: false
      }
    });

    // Create thought resource and method
    const thoughtResource = api.root.addResource('thought');

    // Add POST method to create a thought
    thoughtResource.addMethod('POST', new apigateway.LambdaIntegration(createThoughtLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestModels: {
        'application/json': thoughtRequestModel
      },
      requestValidatorOptions: {
        validateRequestBody: true,
        validateRequestParameters: false
      }
    });
  }
}

module.exports = { CreateThoughtStack };
