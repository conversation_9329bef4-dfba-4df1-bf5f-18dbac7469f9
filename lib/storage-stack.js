const { Stack, RemovalPolicy } = require('aws-cdk-lib');
const dynamodb = require('aws-cdk-lib/aws-dynamodb');

class StorageStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // User Table
    const userTable = new dynamodb.Table(this, 'UserTable', {
      tableName: 'user',
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PROVISIONED,
      readCapacity: 1,
      writeCapacity: 1,
    //   removalPolicy: props?.isProd ? undefined : dynamodb.RemovalPolicy.DESTROY,
    });

    // Add GSIs to User Table
    userTable.addGlobalSecondaryIndex({
      indexName: 'email-index',
      partitionKey: { name: 'email', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.INCLUDE,
      nonKeyAttributes: ['email'],
    });

    userTable.addGlobalSecondaryIndex({
      indexName: 'phoneNumber-countryCode-index',
      partitionKey: { name: 'phoneNumber', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'countryCode', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.INCLUDE,
      nonKeyAttributes: ['phoneNumber', 'countryCode'],
    });

    // Export tables as public properties
    this.userTable = userTable;
  }
}

module.exports = { StorageStack };
