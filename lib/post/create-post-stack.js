const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const apigateway = require('aws-cdk-lib/aws-apigateway');
const s3 = require('aws-cdk-lib/aws-s3');
const iam = require('aws-cdk-lib/aws-iam');
const dynamodb = require('aws-cdk-lib/aws-dynamodb');

class CreatePostStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    const { 
      userPool, 
      isProd,
      profilePicturesBucket,
     } = props;

    // Create DynamoDB table for posts
    const postTable = new dynamodb.Table(this, 'PostTable', {
      tableName: 'post',
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'date_created', type: dynamodb.AttributeType.NUMBER },
      billingMode: dynamodb.BillingMode.PROVISIONED,
      readCapacity: 1,
      writeCapacity: 1,
      // removalPolicy: isProd ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    // Create S3 bucket for post photos
    const postPhotosBucket = new s3.Bucket(this, 'postPhotosBucket', {
      bucketName: `post-photos-${this.account}-${this.region}`,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.PUT,
            s3.HttpMethods.POST,
            s3.HttpMethods.GET,
          ],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
    });

    // Create Lambda function to generate presigned URL
    const generatePresignedUrlLambda = new lambda.Function(this, 'GeneratePresignedUrl', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/generate-presigned-url/function.zip'),
      environment: {
        POST_PHOTOS_BUCKET: postPhotosBucket.bucketName,
      },
    });

    // Create Lambda function to create a post
    const createpostLambda = new lambda.Function(this, 'CreatePost', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/create-post/function.zip'),
      environment: {
        POST_PHOTOS_BUCKET: postPhotosBucket.bucketName,
        POST_TABLE_NAME: postTable.tableName,
      },
    });

    // Grant Lambda permission to generate presigned URLs for the bucket
    const s3PutPolicy = new iam.PolicyStatement({
      actions: ['s3:PutObject'],
      resources: [postPhotosBucket.arnForObjects('*')],
    });
    generatePresignedUrlLambda.addToRolePolicy(s3PutPolicy);

    postPhotosBucket.grantReadWrite(createpostLambda);

    // Grant Lambda permission to write to the post table
    postTable.grantWriteData(createpostLambda);

    // Create Lambda function to get posts by usernames
    const getPostsByUserLambda = new lambda.Function(this, 'GetPostsByUser', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      timeout: Duration.seconds(30),
      code: lambda.Code.fromAsset('lambda/get-posts-by-user/function.zip'),
      environment: {
        POST_TABLE_NAME: postTable.tableName,
        POST_PHOTOS_BUCKET: postPhotosBucket.bucketName,
        PROFILE_PICTURES_BUCKET: profilePicturesBucket.bucketName,
      },
    });

    // Grant Lambda permission to read from the post table
    postTable.grantReadData(getPostsByUserLambda);
    
    // Grant Lambda permission to generate presigned URLs for the bucket
    postPhotosBucket.grantRead(getPostsByUserLambda);

    profilePicturesBucket.grantRead(getPostsByUserLambda);

    // Create API Gateway with Cognito Authorizer
    const api = new apigateway.RestApi(this, 'postAPI', {
      restApiName: 'post API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [...apigateway.Cors.DEFAULT_HEADERS, 'Authorization', 'Content-Type'],
      },
    });

    // Create Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'postAuthorizer', {
      cognitoUserPools: [userPool],
    });

    // Create upload-url resource and method
    const uploadUrlResource = api.root.addResource('upload-url');
    
    // POST method to accept request body
    uploadUrlResource.addMethod('POST', new apigateway.LambdaIntegration(generatePresignedUrlLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    // Create post model
    const postModel = api.addModel('postModel', {
      contentType: 'application/json',
      modelName: 'postModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['moments'],
        properties: {
          title: { type: apigateway.JsonSchemaType.STRING },
          description: { type: apigateway.JsonSchemaType.STRING },
          hashtags: { 
            type: apigateway.JsonSchemaType.ARRAY,
            items: { type: apigateway.JsonSchemaType.STRING }
          },
          moments: {
            type: apigateway.JsonSchemaType.ARRAY,
            items: {
              type: apigateway.JsonSchemaType.OBJECT,
              required: ['resourceKey'],
              properties: {
                title: { type: apigateway.JsonSchemaType.STRING },
                date: { type: apigateway.JsonSchemaType.NUMBER },
                location: { type: apigateway.JsonSchemaType.STRING },
                resourceKey: { type: apigateway.JsonSchemaType.STRING },
              },
            }
          },
        },
      },
    });

    // Create post resource and method
    const postResource = api.root.addResource('post');
    
    // Add POST method to create a post
    postResource.addMethod('POST', new apigateway.LambdaIntegration(createpostLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestValidator: new apigateway.RequestValidator(this, 'postValidator', {
        restApi: api,
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      requestModels: {
        'application/json': postModel,
      },
    });

    // Create model for get posts by user request
    const userListModel = api.addModel('UserListModel', {
      contentType: 'application/json',
      modelName: 'UserListModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        properties: {
          usernames: {
            type: apigateway.JsonSchemaType.ARRAY,
            items: { type: apigateway.JsonSchemaType.STRING }
          },
          limit: { type: apigateway.JsonSchemaType.INTEGER }
        },
      },
    });

    // Create post/by/user resource and method
    const postByResource = postResource.addResource('by');
    const postByUserResource = postByResource.addResource('user');
    
    // Add POST method to get posts by usernames
    postByUserResource.addMethod('POST', new apigateway.LambdaIntegration(getPostsByUserLambda), {
      authorizer: authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestValidator: new apigateway.RequestValidator(this, 'postByUserValidator', {
        restApi: api,
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      requestModels: {
        'application/json': userListModel,
      },
    });

    // Export resources as public properties
    this.postPhotosBucket = postPhotosBucket;
    this.postTable = postTable;
    this.postApi = api;
  }
}

module.exports = { CreatePostStack };
