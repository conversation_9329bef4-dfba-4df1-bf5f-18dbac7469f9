const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const apigateway = require('aws-cdk-lib/aws-apigateway');

class LoginStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // Extract user pool and client from props
    const { userPool, userPoolClient } = props;

    // Create Login Lambda
    const loginFunction = new lambda.Function(this, 'LoginFunction', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      code: lambda.Code.fromAsset('lambda/login/function.zip'),
      timeout: Duration.seconds(30),
      environment: {
        USER_POOL_ID: userPool.userPoolId,
        CLIENT_ID: userPoolClient.userPoolClientId,
      },
    });

    // Grant Cognito permissions to the Lambda
    userPool.grant(loginFunction, 
      'cognito-idp:InitiateAuth'
    );

    // Create API Gateway
    const api = new apigateway.RestApi(this, 'LoginApi', {
      restApiName: 'Login API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_ORIGINS,
      },
    });

    // Create login model
    const loginModel = api.addModel('LoginModel', {
      contentType: 'application/json',
      modelName: 'LoginModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['username', 'password'],
        properties: {
          username: { type: apigateway.JsonSchemaType.STRING },
          password: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    // Create login resource and method
    const loginResource = api.root.addResource('login');
    loginResource.addMethod('POST', new apigateway.LambdaIntegration(loginFunction), {
      requestValidator: new apigateway.RequestValidator(this, 'LoginValidator', {
        restApi: api,
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      requestModels: {
        'application/json': loginModel,
      },
    });
  }
}

module.exports = { LoginStack };