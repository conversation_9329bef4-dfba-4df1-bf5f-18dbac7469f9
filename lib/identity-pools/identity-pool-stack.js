const { Stack } = require('aws-cdk-lib');
const cognito = require('aws-cdk-lib/aws-cognito');
const iam = require('aws-cdk-lib/aws-iam');

class IdentityPoolStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    const {
      userPool,
      userPoolClient,
    } = props;

    // Create Identity Pool
    const identityPool = new cognito.CfnIdentityPool(this, 'JourneyIdentityPool', {
      allowUnauthenticatedIdentities: false,
      cognitoIdentityProviders: [{
        clientId: userPoolClient.userPoolClientId,
        providerName: userPool.userPoolProviderName,
      }],
    });

    // Create authenticated role
    const authenticatedRole = new iam.Role(this, 'CognitoDefaultAuthenticatedRole', {
      assumedBy: new iam.FederatedPrincipal(
        'cognito-identity.amazonaws.com',
        {
          StringEquals: {
            'cognito-identity.amazonaws.com:aud': identityPool.ref,
          },
          'ForAnyValue:StringLike': {
            'cognito-identity.amazonaws.com:amr': 'authenticated',
          },
        },
        'sts:AssumeRoleWithWebIdentity',
      ),
    });

    authenticatedRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        's3:PutObject',
        's3:GetObject',
      ],
      resources: [
        'arn:aws:s3:::journey-profile-pictures/standard/*',
        'arn:aws:s3:::journey-profile-pictures/small/*',
      ],
    }));

    //arn:aws:s3:::journey-profile-pictures

    // Attach roles to Identity Pool
    new cognito.CfnIdentityPoolRoleAttachment(this, 'IdentityPoolRoleAttachment', {
      identityPoolId: identityPool.ref,
      roles: {
        authenticated: authenticatedRole.roleArn,
      },
    });

    // const bucketPolicy = new iam.PolicyStatement({
    //   effect: iam.Effect.ALLOW,
    //   principals: [
    //     new iam.FederatedPrincipal(
    //       'cognito-identity.amazonaws.com',
    //       {
    //         StringEquals: {
    //           'cognito-identity.amazonaws.com:aud': identityPool.ref,
    //         },
    //         'ForAnyValue:StringLike': {
    //           'cognito-identity.amazonaws.com:amr': 'authenticated',
    //         },
    //       },
    //       'sts:AssumeRoleWithWebIdentity'
    //     ),
    //   ],
    //   actions: [
    //     's3:GetObject',
    //   ],
    //   resources: [
    //     'arn:aws:s3:::journey-profile-pictures/standard/*',
    //     'arn:aws:s3:::journey-profile-pictures/small/*',
    //   ],
    // });

    // profilePicturesBucket.addToResourcePolicy(bucketPolicy);

    this.identityPool = identityPool;
    this.authenticatedRole = authenticatedRole;
  }
}

module.exports = { IdentityPoolStack };
