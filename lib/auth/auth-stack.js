const { Stack, Duration } = require('aws-cdk-lib');
const lambda = require('aws-cdk-lib/aws-lambda');
const apigateway = require('aws-cdk-lib/aws-apigateway');

class AuthStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // Extract user pool and client from props
    const { userPool, userPoolClient } = props;

    // Create Refresh Token Lambda
    const refreshTokenFunction = new lambda.Function(this, 'RefreshTokenFunction', {
      runtime: lambda.Runtime.PROVIDED_AL2023,
      handler: 'bootstrap',
      code: lambda.Code.fromAsset('lambda/refresh-token/function.zip'),
      timeout: Duration.seconds(30),
      environment: {
        CLIENT_ID: userPoolClient.userPoolClientId,
      },
    });

    // Grant Cognito permissions to the Lambda
    userPool.grant(refreshTokenFunction, 
      'cognito-idp:InitiateAuth'
    );

    // Create API Gateway
    const api = new apigateway.RestApi(this, 'AuthApi', {
      restApiName: 'Auth API',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
      },
    });

    // Create refresh token model
    const refreshTokenModel = api.addModel('RefreshTokenModel', {
      contentType: 'application/json',
      modelName: 'RefreshTokenModel',
      schema: {
        type: apigateway.JsonSchemaType.OBJECT,
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: apigateway.JsonSchemaType.STRING },
        },
      },
    });

    // Create refresh-token resource and method
    const refreshTokenResource = api.root.addResource('refresh-token');
    refreshTokenResource.addMethod('POST', new apigateway.LambdaIntegration(refreshTokenFunction), {
      requestValidator: new apigateway.RequestValidator(this, 'RefreshTokenValidator', {
        restApi: api,
        validateRequestBody: true,
        validateRequestParameters: false,
      }),
      requestModels: {
        'application/json': refreshTokenModel,
      },
    });
  }
}

module.exports = { AuthStack };