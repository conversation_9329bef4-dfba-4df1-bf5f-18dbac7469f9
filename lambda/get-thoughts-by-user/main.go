package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client
var s3Client *s3.Client

func init() {
	// Initialize the AWS SDK clients
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
	s3Client = s3.NewFromConfig(cfg)
}

// Thought represents a thought record
type Thought struct {
	Username           string `json:"username"`
	DateCreated        int64  `json:"dateCreated"`
	ThoughtBody        string `json:"thoughtBody"`
	Journey            *int   `json:"journey,omitempty"`
	UserProfilePicture string `json:"userProfilePicture,omitempty"`
}

// Response represents the API response
type Response struct {
	Thoughts []Thought `json:"thoughts"`
	Count    int       `json:"count"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
	if tokenString == "" {
		return "", fmt.Errorf("no token provided")
	}

	// Remove "Bearer " prefix if present
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("failed to extract claims")
	}

	// Extract username from token claims
	username, ok := claims["cognito:username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token")
	}

	return username, nil
}

// Get user profile picture from user table
func getUserProfilePicture(ctx context.Context, username string) (string, error) {
	userTableName := os.Getenv("USER_TABLE_NAME")
	if userTableName == "" {
		return "", fmt.Errorf("USER_TABLE_NAME environment variable not set")
	}

	// Get user item from DynamoDB
	result, err := dynamoClient.GetItem(ctx, &dynamodb.GetItemInput{
		TableName: &userTableName,
		Key: map[string]types.AttributeValue{
			"username": &types.AttributeValueMemberS{Value: username},
		},
	})

	if err != nil {
		return "", fmt.Errorf("failed to get user profile: %v", err)
	}

	// Extract profilePicture attribute if it exists
	if result.Item != nil {
		if val, ok := result.Item["profilePicture"].(*types.AttributeValueMemberS); ok {
			return val.Value, nil
		}
	}

	// Return empty string if no profile picture found
	return "", nil
}

// Generate presigned URL for user profile picture
func generateProfilePicturePresignedUrl(ctx context.Context, profilePictureKey string) (string, error) {
	if profilePictureKey == "" {
		return "", nil
	}

	bucketName := os.Getenv("PROFILE_PICTURES_BUCKET")
	if bucketName == "" {
		return "", fmt.Errorf("PROFILE_PICTURES_BUCKET environment variable not set")
	}

	// Create presigned URL request
	presigner := s3.NewPresignClient(s3Client)
	request, err := presigner.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &profilePictureKey,
	}, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(15 * time.Minute) // URL expires in 15 minutes
	})

	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %v", err)
	}

	return request.URL, nil
}

// Get thoughts by username with optional date filtering
func getThoughtsByUser(ctx context.Context, username string, startDate, endDate *int64) ([]Thought, error) {
	tableName := os.Getenv("THOUGHT_TABLE_NAME")
	if tableName == "" {
		return nil, fmt.Errorf("THOUGHT_TABLE_NAME environment variable not set")
	}

	// Build the key condition expression with date filtering on sort key
	var keyCond expression.KeyConditionBuilder

	if startDate != nil && endDate != nil {
		// Both start and end dates provided
		keyCond = expression.Key("username").Equal(expression.Value(username)).
			And(expression.Key("date_created").Between(expression.Value(*startDate), expression.Value(*endDate)))
	} else if startDate != nil {
		// Only start date provided
		keyCond = expression.Key("username").Equal(expression.Value(username)).
			And(expression.Key("date_created").GreaterThanEqual(expression.Value(*startDate)))
	} else if endDate != nil {
		// Only end date provided
		keyCond = expression.Key("username").Equal(expression.Value(username)).
			And(expression.Key("date_created").LessThanEqual(expression.Value(*endDate)))
	} else {
		// No date filtering
		keyCond = expression.Key("username").Equal(expression.Value(username))
	}

	// Build the expression
	expr, err := expression.NewBuilder().
		WithKeyCondition(keyCond).
		Build()

	if err != nil {
		return nil, fmt.Errorf("failed to build expression: %v", err)
	}

	// Create query input
	input := &dynamodb.QueryInput{
		TableName:                 &tableName,
		KeyConditionExpression:    expr.KeyCondition(),
		ExpressionAttributeNames:  expr.Names(),
		ExpressionAttributeValues: expr.Values(),
		ScanIndexForward:          new(bool), // false = descending order (newest first)
	}

	// Execute query
	result, err := dynamoClient.Query(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to query thoughts: %v", err)
	}

	// Parse results
	var thoughts []Thought
	for _, item := range result.Items {
		thought := Thought{}

		// Extract username
		if val, ok := item["username"].(*types.AttributeValueMemberS); ok {
			thought.Username = val.Value
		}

		// Extract date_created
		if val, ok := item["date_created"].(*types.AttributeValueMemberN); ok {
			dateStr := val.Value
			if dateCreated, err := strconv.ParseInt(dateStr, 10, 64); err == nil {
				thought.DateCreated = dateCreated
			}
		}

		// Extract thought_body
		if val, ok := item["thought_body"].(*types.AttributeValueMemberS); ok {
			thought.ThoughtBody = val.Value
		}

		// Extract journey (optional)
		if val, ok := item["journey"].(*types.AttributeValueMemberN); ok {
			if journey, err := strconv.Atoi(val.Value); err == nil {
				thought.Journey = &journey
			}
		}

		thoughts = append(thoughts, thought)
	}

	return thoughts, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Determine username: check query parameter first, then extract from token
	var username string
	var err error

	// Check if username is provided as a query parameter
	if usernameParam, ok := request.QueryStringParameters["username"]; ok && usernameParam != "" {
		username = usernameParam

		// Still need to validate the token for authorization
		_, err = extractUsername(request.Headers["Authorization"])
		if err != nil {
			return events.APIGatewayProxyResponse{
				StatusCode: 401,
				Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	} else {
		// Extract username from token if not provided as query parameter
		username, err = extractUsername(request.Headers["Authorization"])
		if err != nil {
			return events.APIGatewayProxyResponse{
				StatusCode: 401,
				Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	// Parse optional query parameters
	var startDate, endDate *int64

	if startDateStr, ok := request.QueryStringParameters["startDate"]; ok && startDateStr != "" {
		if len(startDateStr) > 10 {
			startDateStr = startDateStr[:10]
		}
		if parsed, err := strconv.ParseInt(startDateStr, 10, 64); err == nil {
			startDate = &parsed
		} else {
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       `{"error": "Invalid startDate format. Must be a Unix timestamp"}`,
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	if endDateStr, ok := request.QueryStringParameters["endDate"]; ok && endDateStr != "" {
		if len(endDateStr) > 10 {
			endDateStr = endDateStr[:10]
		}
		if parsed, err := strconv.ParseInt(endDateStr, 10, 64); err == nil {
			endDate = &parsed
		} else {
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       `{"error": "Invalid endDate format. Must be a Unix timestamp"}`,
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	// Get user profile picture
	profilePictureKey, err := getUserProfilePicture(ctx, username)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get user profile: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Generate presigned URL for profile picture (only once for all thoughts)
	profilePicturePresignedUrl, err := generateProfilePicturePresignedUrl(ctx, profilePictureKey)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to generate profile picture URL: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Get thoughts
	thoughts, err := getThoughtsByUser(ctx, username, startDate, endDate)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get thoughts: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Add the profile picture presigned URL to all thoughts
	for i := range thoughts {
		thoughts[i].UserProfilePicture = profilePicturePresignedUrl
	}

	if (thoughts == nil) {
		thoughts = []Thought{}
	}

	// Create response
	response := Response{
		Thoughts: thoughts,
		Count:    len(thoughts),
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
