package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"
    "strings"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
    "github.com/aws/aws-sdk-go-v2/service/s3"
    "github.com/golang-jwt/jwt/v5"
)

type UserProfile struct {
    Username             string `json:"username,omitempty"`
    FirstName            string `json:"firstName,omitempty"`
    LastName             string `json:"lastName,omitempty"`
    CurrentLocation      string `json:"currentLocation,omitempty"`
    Bio                  string `json:"bio,omitempty"`
    ProfilePicture       string `json:"profilePicture,omitempty"`
    ProfilePictureSmall  string `json:"profilePictureSmall,omitempty"`
    ProfileBannerPicture string `json:"bannerPicture,omitempty"`
}

var (
    dynamoClient      *dynamodb.Client
    s3Client          *s3.Client
    userTableName     string
    pictureBucketName string
    bannerBucketName  string
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
    s3Client = s3.NewFromConfig(cfg)
    userTableName = os.Getenv("USER_TABLE_NAME")
    pictureBucketName = os.Getenv("PROFILE_PICTURES_BUCKET")
    bannerBucketName = os.Getenv("PROFILE_BANNER_PICTURES_BUCKET")
}

func extractUsername(authHeader string) (string, error) {
    // Format: "Bearer <token>"
    parts := strings.Split(authHeader, " ")
    if len(parts) != 2 || parts[0] != "Bearer" {
        return "", fmt.Errorf("invalid authorization header format")
    }

    tokenString := parts[1]
    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
        return "", fmt.Errorf("failed to parse token: %v", err)
    }

    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
        return "", fmt.Errorf("failed to extract claims")
    }

    username, ok := claims["cognito:username"].(string)
    if !ok {
        return "", fmt.Errorf("username not found in token")
    }

    return username, nil
}

func createPresignedUrl(ctx context.Context, key string, bucketName string) (string, error) {
    if key == "" {
        return "", nil
    }

    // Create presigner
    presignClient := s3.NewPresignClient(s3Client)
    
    // Create the presigned URL with an expiration time of 1 hour
    presignedReq, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
        Bucket: aws.String(bucketName),
        Key:    aws.String(key),
    }, func(opts *s3.PresignOptions) {
        opts.Expires = 1 * time.Hour
    })
    
    if err != nil {
        return "", fmt.Errorf("failed to create presigned URL: %v", err)
    }
    
    return presignedReq.URL, nil
}

func getUserProfile(ctx context.Context, username string) (*UserProfile, error) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String(userTableName),
        Key: map[string]types.AttributeValue{
            "username": &types.AttributeValueMemberS{Value: username},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        return nil, err
    }

    if len(result.Item) == 0 {
        return nil, fmt.Errorf("user not found")
    }

    fmt.Println("result.Item: ", result.Item)

    profile := &UserProfile{
        Username: username,
    }

    // Extract fields if they exist
    if firstName, ok := result.Item["firstName"]; ok {
        if v, ok := firstName.(*types.AttributeValueMemberS); ok {
            profile.FirstName = v.Value
        }
    }
    
    if lastName, ok := result.Item["lastName"]; ok {
        if v, ok := lastName.(*types.AttributeValueMemberS); ok {
            profile.LastName = v.Value
        }
    }
    
    if currentLocation, ok := result.Item["currentLocation"]; ok {
        if v, ok := currentLocation.(*types.AttributeValueMemberS); ok {
            profile.CurrentLocation = v.Value
        }
    }
    
    if bio, ok := result.Item["bio"]; ok {
        if v, ok := bio.(*types.AttributeValueMemberS); ok {
            profile.Bio = v.Value
        }
    }
    
    // Get profile picture keys
    var profilePictureKey, profilePictureSmallKey, profileBannerPictureKey string
    
    if profilePicture, ok := result.Item["profilePicture"]; ok {
        if v, ok := profilePicture.(*types.AttributeValueMemberS); ok {
            profilePictureKey = v.Value
        }
    }
    
    if profilePictureSmall, ok := result.Item["profilePictureSmall"]; ok {
        if v, ok := profilePictureSmall.(*types.AttributeValueMemberS); ok {
            profilePictureSmallKey = v.Value
        }
    }
    
    if profileBannerPicture, ok := result.Item["profileBannerPicture"]; ok {
        if v, ok := profileBannerPicture.(*types.AttributeValueMemberS); ok {
            profileBannerPictureKey = v.Value
        }
    }
    
    // Create presigned URLs for profile pictures
    if profilePictureKey != "" {
        presignedUrl, err := createPresignedUrl(ctx, profilePictureKey, pictureBucketName)
        if err != nil {
            // Log the error but continue
            fmt.Printf("Error creating presigned URL for profile picture: %v\n", err)
        } else {
            profile.ProfilePicture = presignedUrl
        }
    }
    
    if profilePictureSmallKey != "" {
        presignedUrl, err := createPresignedUrl(ctx, profilePictureSmallKey, pictureBucketName)
        if err != nil {
            // Log the error but continue
            fmt.Printf("Error creating presigned URL for small profile picture: %v\n", err)
        } else {
            profile.ProfilePictureSmall = presignedUrl
        }
    }
    
    // Create presigned URL for banner picture
    if profileBannerPictureKey != "" {
        presignedUrl, err := createPresignedUrl(ctx, profileBannerPictureKey, bannerBucketName)
        if err != nil {
            // Log the error but continue
            fmt.Printf("Error creating presigned URL for banner picture: %v\n", err)
        } else {
            profile.ProfileBannerPicture = presignedUrl
        }
    }

    return profile, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Extract authorization token
    authHeader := request.Headers["Authorization"]
    if authHeader == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       `{"error": "Missing authorization token"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Extract username from token
    username, err := extractUsername(authHeader)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Get user profile
    profile, err := getUserProfile(ctx, username)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 404,
            Body:       fmt.Sprintf(`{"error": "Failed to get user profile: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Convert profile to JSON
    responseBody, err := json.Marshal(profile)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to serialize response: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers:    map[string]string{"Content-Type": "application/json"},
    }, nil
}

func main() {
    lambda.Start(handler)
}
