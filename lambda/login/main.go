package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
    "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
)

type Request struct {
    Username string `json:"username"`
    Password string `json:"password"`
}

type UserProfile struct {
    Username             string `json:"username,omitempty"`
    FirstName            string `json:"firstName,omitempty"`
    LastName             string `json:"lastName,omitempty"`
    CurrentLocation      string `json:"currentLocation,omitempty"`
    Bio                  string `json:"bio,omitempty"`
    ProfilePicture       string `json:"profilePicture,omitempty"`
    ProfilePictureSmall  string `json:"profilePictureSmall,omitempty"`
    ProfileBannerPicture string `json:"bannerPicture,omitempty"`
}

type Response struct {
    IdToken      string `json:"idToken,omitempty"`
    AccessToken  string `json:"accessToken,omitempty"`
    RefreshToken string `json:"refreshToken,omitempty"`
    UserProfile  UserProfile `json:"userProfile,omitempty"`
    Error        string `json:"error,omitempty"`
}

var (
    cognitoClient *cognitoidentityprovider.Client
    userPoolId    string
    clientId      string
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    cognitoClient = cognitoidentityprovider.NewFromConfig(cfg)
    userPoolId = os.Getenv("USER_POOL_ID")
    clientId = os.Getenv("CLIENT_ID")
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    if req.Username == "" || req.Password == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Username and password are required"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    input := &cognitoidentityprovider.InitiateAuthInput{
        AuthFlow: types.AuthFlowTypeUserPasswordAuth,
        ClientId: aws.String(clientId),
        AuthParameters: map[string]string{
            "USERNAME": req.Username,
            "PASSWORD": req.Password,
        },
    }

    result, err := cognitoClient.InitiateAuth(ctx, input)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Authentication failed: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    response := Response{
        IdToken:      *result.AuthenticationResult.IdToken,
        AccessToken:  *result.AuthenticationResult.AccessToken,
        RefreshToken: *result.AuthenticationResult.RefreshToken,
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Internal server error"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers:    map[string]string{"Content-Type": "application/json"},
    }, nil
}

func main() {
    lambda.Start(handler)
}