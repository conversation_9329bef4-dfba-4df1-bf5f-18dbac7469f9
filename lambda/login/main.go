package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	cognitoTypes "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
    dynamoDBTypes "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type Request struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// struct is copied from get-user-profile, update project structure later
type UserProfile struct {
	Username             string `json:"username,omitempty"`
	FirstName            string `json:"firstName,omitempty"`
	LastName             string `json:"lastName,omitempty"`
	CurrentLocation      string `json:"currentLocation,omitempty"`
	Bio                  string `json:"bio,omitempty"`
	ProfilePicture       string `json:"profilePicture,omitempty"`
	ProfilePictureSmall  string `json:"profilePictureSmall,omitempty"`
	ProfileBannerPicture string `json:"bannerPicture,omitempty"`
}

type Response struct {
	IdToken      string      `json:"idToken,omitempty"`
	AccessToken  string      `json:"accessToken,omitempty"`
	RefreshToken string      `json:"refreshToken,omitempty"`
	UserProfile  UserProfile `json:"userProfile,omitempty"`
	Error        string      `json:"error,omitempty"`
}

var (
	dynamoClient      *dynamodb.Client
	s3Client          *s3.Client
	userTableName     string
	pictureBucketName string
	bannerBucketName  string
	cognitoClient *cognitoidentityprovider.Client
	userPoolId    string
	clientId      string
)

// method is copied from get-user-profile, update project structure later
func createPresignedUrl(ctx context.Context, key string, bucketName string) (string, error) {
    if key == "" {
        return "", nil
    }

    // Create presigner
    presignClient := s3.NewPresignClient(s3Client)
    
    // Create the presigned URL with an expiration time of 1 hour
    presignedReq, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
        Bucket: aws.String(bucketName),
        Key:    aws.String(key),
    }, func(opts *s3.PresignOptions) {
        opts.Expires = 1 * time.Hour
    })
    
    if err != nil {
        return "", fmt.Errorf("failed to create presigned URL: %v", err)
    }
    
    return presignedReq.URL, nil
}

// method is copied from get-user-profile, update project structure later
func getUserProfile(ctx context.Context, username string) (*UserProfile, error) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String(userTableName),
        Key: map[string]dynamoDBTypes.AttributeValue{
            "username": &dynamoDBTypes.AttributeValueMemberS{Value: username},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        return nil, err
    }

    if len(result.Item) == 0 {
        return nil, fmt.Errorf("user not found")
    }

    fmt.Println("result.Item: ", result.Item)

    profile := &UserProfile{
        Username: username,
    }

    // Extract fields if they exist
    if firstName, ok := result.Item["firstName"]; ok {
        if v, ok := firstName.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profile.FirstName = v.Value
        }
    }
    
    if lastName, ok := result.Item["lastName"]; ok {
        if v, ok := lastName.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profile.LastName = v.Value
        }
    }
    
    if currentLocation, ok := result.Item["currentLocation"]; ok {
        if v, ok := currentLocation.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profile.CurrentLocation = v.Value
        }
    }
    
    if bio, ok := result.Item["bio"]; ok {
        if v, ok := bio.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profile.Bio = v.Value
        }
    }
    
    // Get profile picture keys
    var profilePictureKey, profilePictureSmallKey, profileBannerPictureKey string
    
    if profilePicture, ok := result.Item["profilePicture"]; ok {
        if v, ok := profilePicture.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profilePictureKey = v.Value
        }
    }
    
    if profilePictureSmall, ok := result.Item["profilePictureSmall"]; ok {
        if v, ok := profilePictureSmall.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profilePictureSmallKey = v.Value
        }
    }
    
    if profileBannerPicture, ok := result.Item["profileBannerPicture"]; ok {
        if v, ok := profileBannerPicture.(*dynamoDBTypes.AttributeValueMemberS); ok {
            profileBannerPictureKey = v.Value
        }
    }
    
    // Create presigned URLs for profile pictures
    if profilePictureKey != "" {
        presignedUrl, err := createPresignedUrl(ctx, profilePictureKey, pictureBucketName)
        if err != nil {
            // Log the error but continue
            fmt.Printf("Error creating presigned URL for profile picture: %v\n", err)
        } else {
            profile.ProfilePicture = presignedUrl
        }
    }
    
    if profilePictureSmallKey != "" {
        presignedUrl, err := createPresignedUrl(ctx, profilePictureSmallKey, pictureBucketName)
        if err != nil {
            // Log the error but continue
            fmt.Printf("Error creating presigned URL for small profile picture: %v\n", err)
        } else {
            profile.ProfilePictureSmall = presignedUrl
        }
    }
    
    // Create presigned URL for banner picture
    if profileBannerPictureKey != "" {
        presignedUrl, err := createPresignedUrl(ctx, profileBannerPictureKey, bannerBucketName)
        if err != nil {
            // Log the error but continue
            fmt.Printf("Error creating presigned URL for banner picture: %v\n", err)
        } else {
            profile.ProfileBannerPicture = presignedUrl
        }
    }

    return profile, nil
}

func init() {
	cfg, err := config.LoadDefaultConfig(context.Background())
	if err != nil {
		panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
	s3Client = s3.NewFromConfig(cfg)
	userTableName = os.Getenv("USER_TABLE_NAME")
	pictureBucketName = os.Getenv("PROFILE_PICTURES_BUCKET")
	bannerBucketName = os.Getenv("PROFILE_BANNER_PICTURES_BUCKET")
	cognitoClient = cognitoidentityprovider.NewFromConfig(cfg)
	userPoolId = os.Getenv("USER_POOL_ID")
	clientId = os.Getenv("CLIENT_ID")
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	var req Request
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error": "Invalid request body"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	if req.Username == "" || req.Password == "" {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error": "Username and password are required"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	input := &cognitoidentityprovider.InitiateAuthInput{
		AuthFlow: cognitoTypes.AuthFlowTypeUserPasswordAuth,
		ClientId: aws.String(clientId),
		AuthParameters: map[string]string{
			"USERNAME": req.Username,
			"PASSWORD": req.Password,
		},
	}

	result, err := cognitoClient.InitiateAuth(ctx, input)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 401,
			Body:       fmt.Sprintf(`{"error": "Authentication failed: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Get user profile
	profile, err := getUserProfile(ctx, req.Username)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get user profile: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	response := Response{
		IdToken:      *result.AuthenticationResult.IdToken,
		AccessToken:  *result.AuthenticationResult.AccessToken,
		RefreshToken: *result.AuthenticationResult.RefreshToken,
		UserProfile:  *profile,
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Internal server error"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers:    map[string]string{"Content-Type": "application/json"},
	}, nil
}

func main() {
	lambda.Start(handler)
}
