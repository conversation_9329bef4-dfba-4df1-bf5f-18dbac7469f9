package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "os"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/s3"
    "github.com/golang-jwt/jwt/v5"
    "github.com/google/uuid"
)

var s3Client *s3.Client

func init() {
    // Initialize the S3 client
    cfg, err := config.LoadDefaultConfig(context.TODO())
    if err != nil {
        log.Fatalf("unable to load SDK config, %v", err)
    }
    s3Client = s3.NewFromConfig(cfg)
}

// Request represents the API request parameters
type Request struct {
    JourneyKey string `json:"journeyKey,omitempty"` // Optional: if not provided, will generate UUID
}

// Response represents the API response
type Response struct {
    PreSignedUrl string `json:"preSignedUrl"`
    Key          string `json:"key"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
    if tokenString == "" {
        return "", fmt.Errorf("no token provided")
    }

    // Remove "Bearer " prefix if present
    if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
        tokenString = tokenString[7:]
    }

    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
        return "", fmt.Errorf("failed to parse token: %v", err)
    }

    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
        return "", fmt.Errorf("failed to extract claims")
    }

    // Extract username from token claims
    username, ok := claims["cognito:username"].(string)
    if !ok {
        return "", fmt.Errorf("username not found in token")
    }

    return username, nil
}

// Generate a presigned URL for uploading a cover photo
func generatePresignedUrl(ctx context.Context, bucket, key string) (string, error) {
    presignClient := s3.NewPresignClient(s3Client)
    
    // Create presigned PUT URL with 15-minute expiration
    presignedReq, err := presignClient.PresignPutObject(ctx, &s3.PutObjectInput{
        Bucket: &bucket,
        Key:    &key,
    }, func(opts *s3.PresignOptions) {
        opts.Expires = 15 * time.Minute
    })
    
    if err != nil {
        return "", fmt.Errorf("failed to create presigned URL: %v", err)
    }
    
    return presignedReq.URL, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Extract username from token
    username, err := extractUsername(request.Headers["Authorization"])
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Get bucket name from environment variable
    bucketName := os.Getenv("JOURNEY_COVER_PHOTOS_BUCKET")
    if bucketName == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Bucket name not configured"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Parse request body (optional)
    var req Request
    if request.Body != "" {
        if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
            return events.APIGatewayProxyResponse{
                StatusCode: 400,
                Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
                Headers:    map[string]string{"Content-Type": "application/json"},
            }, nil
        }
    }

    // Generate journey key if not provided
    journeyKey := req.JourneyKey
    if journeyKey == "" {
        journeyKey = uuid.New().String()
    }

    // Generate a unique key for the cover photo
    // Format: journey-covers/{username}/{journeyKey}/cover.jpg
    key := fmt.Sprintf("journey-covers/%s/%s/cover.jpg", username, journeyKey)

    // Generate presigned URL
    presignedUrl, err := generatePresignedUrl(ctx, bucketName, key)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to generate presigned URL: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Create response
    response := Response{
        PreSignedUrl: presignedUrl,
        Key:          key,
    }

    // Convert response to JSON
    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to create response: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}
