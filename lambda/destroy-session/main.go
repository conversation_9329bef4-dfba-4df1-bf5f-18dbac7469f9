package main

import (
    "context"
    "encoding/json"
    "fmt"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

// Request represents the expected JSON payload
type Request struct {
    SessionToken string `json:"sessionToken"`
}

// Response represents the Lambda response structure
type Response struct {
    Message string `json:"message"`
}

var (
    dynamoClient *dynamodb.Client
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
}

func destroySession(ctx context.Context, sessionToken string) error {
    input := &dynamodb.DeleteItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
    }

    _, err := dynamoClient.DeleteItem(ctx, input)
    return err
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Validate request
    if req.SessionToken == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "SessionToken is required"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Attempt to destroy the session
    if err := destroySession(ctx, req.SessionToken); err != nil {
        // Log the error but still return success
        fmt.Printf("Error deleting session: %v\n", err)
    }

    // Create success response
    response := Response{
        Message: "Session destroyed",
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Internal server error"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}