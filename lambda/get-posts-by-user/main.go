package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "os"
    "sort"
    "sync"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
    "github.com/aws/aws-sdk-go-v2/service/s3"
)

var (
    dynamoClient *dynamodb.Client
    s3Client     *s3.Client
)

func init() {
    // Initialize the AWS SDK clients
    cfg, err := config.LoadDefaultConfig(context.TODO())
    if err != nil {
        log.Fatalf("unable to load SDK config, %v", err)
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
    s3Client = s3.NewFromConfig(cfg)
}

// Request represents the API request
type Request struct {
    Usernames []string `json:"usernames"`
    Limit     *int     `json:"limit,omitempty"`
}

// Moment represents a single moment in a journey
type Moment struct {
    Title       *string `json:"title,omitempty"`
    Date        *string `json:"date,omitempty"`
    Location    *string `json:"location,omitempty"`
    ResourceKey string  `json:"resourceKey"`
}

// Journey represents a journey post
type Journey struct {
    Username      string    `json:"username"`
    DateCreated   int64     `json:"dateCreated"`
    Title         *string   `json:"title,omitempty"`
    Description   *string   `json:"description,omitempty"`
    Hashtags      []string  `json:"hashtags,omitempty"`
    Moments       []Moment  `json:"moments"`
    ProfilePicture string   `json:"profilePicture,omitempty"`
}

// Moment represents a single moment in a journey
type MomentResponse struct {
    Title       *string `json:"title,omitempty"`
    Date        *string `json:"date,omitempty"`
    Location    *string `json:"location,omitempty"`
    ImageUrl string  `json:"imageUrl"`
}

// Journey represents a journey post
type JourneyResponse struct {
    Username      string    `json:"username"`
    DateCreated   int64     `json:"dateCreated"`
    Title         *string   `json:"title,omitempty"`
    Description   *string   `json:"description,omitempty"`
    Hashtags      []string  `json:"hashtags,omitempty"`
    Moments       []MomentResponse  `json:"moments"`
    ProfilePicture string   `json:"profilePicture,omitempty"`
}

// Get journeys by usernames
func getJourneysByUsernames(ctx context.Context, usernames []string, limit *int) ([]Journey, error) {
    tableName := os.Getenv("JOURNEY_TABLE_NAME")
    if tableName == "" {
        return nil, fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
    }

    var allJourneys []Journey

    // Query each username
    for _, username := range usernames {
        // Create key condition expression
        keyCond := expression.Key("username").Equal(expression.Value(username))
        expr, err := expression.NewBuilder().WithKeyCondition(keyCond).Build()
        if err != nil {
            return nil, fmt.Errorf("failed to build expression: %v", err)
        }

        // Create query input
        input := &dynamodb.QueryInput{
            TableName:                 &tableName,
            KeyConditionExpression:    expr.KeyCondition(),
            ExpressionAttributeNames:  expr.Names(),
            ExpressionAttributeValues: expr.Values(),
            ScanIndexForward:          new(bool), // false = descending order (newest first)
        }

        // Execute query
        result, err := dynamoClient.Query(ctx, input)
        if err != nil {
            return nil, fmt.Errorf("failed to query journeys: %v", err)
        }

        // Process results
        for _, item := range result.Items {
            journey, err := unmarshalJourney(item)
            if err != nil {
                log.Printf("Error unmarshaling journey: %v", err)
                continue
            }
            
            // Set the username for profile picture lookup
            journey.Username = username
            
            allJourneys = append(allJourneys, journey)
        }
    }

    // Sort all journeys by date_created in descending order
    sort.Slice(allJourneys, func(i, j int) bool {
        return allJourneys[i].DateCreated > allJourneys[j].DateCreated
    })

    // Apply limit if provided
    if limit != nil && len(allJourneys) > *limit {
        allJourneys = allJourneys[:*limit]
    }

    return allJourneys, nil
}

// Unmarshal DynamoDB item to Journey
func unmarshalJourney(item map[string]types.AttributeValue) (Journey, error) {
    var journey Journey

    // Extract username
    if username, ok := item["username"].(*types.AttributeValueMemberS); ok {
        journey.Username = username.Value
    } else {
        return journey, fmt.Errorf("username not found or not a string")
    }

    // Extract date_created
    if dateCreated, ok := item["date_created"].(*types.AttributeValueMemberN); ok {
        var err error
        journey.DateCreated, err = json.Number(dateCreated.Value).Int64()
        if err != nil {
            return journey, fmt.Errorf("failed to parse date_created: %v", err)
        }
    } else {
        return journey, fmt.Errorf("date_created not found or not a number")
    }

    // Extract title (optional)
    if title, ok := item["title"].(*types.AttributeValueMemberS); ok {
        titleStr := title.Value
        journey.Title = &titleStr
    }

    // Extract description (optional)
    if desc, ok := item["description"].(*types.AttributeValueMemberS); ok {
        descStr := desc.Value
        journey.Description = &descStr
    }

    // Extract hashtags (optional)
    if hashtags, ok := item["hashtags"].(*types.AttributeValueMemberS); ok {
        var hashtagList []string
        if err := json.Unmarshal([]byte(hashtags.Value), &hashtagList); err != nil {
            return journey, fmt.Errorf("failed to unmarshal hashtags: %v", err)
        }
        journey.Hashtags = hashtagList
    }

    // Extract moments
    if moments, ok := item["moments"].(*types.AttributeValueMemberS); ok {
        var momentList []Moment
        if err := json.Unmarshal([]byte(moments.Value), &momentList); err != nil {
            return journey, fmt.Errorf("failed to unmarshal moments: %v", err)
        }
        journey.Moments = momentList
    } else {
        return journey, fmt.Errorf("moments not found or not a string")
    }

    return journey, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Validate request
    if len(req.Usernames) == 0 {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "At least one username is required"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Get journeys by usernames
    journeys, err := getJourneysByUsernames(ctx, req.Usernames, req.Limit)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to get journeys: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Generate presigned URLs for all resource keys
    if err := generatePresignedUrls(ctx, journeys); err != nil {
        log.Printf("Warning: Failed to generate some presigned URLs for journey photos: %v", err)
        // Continue with the journeys we have
    }
    
    // Generate presigned URLs for profile pictures
    if err := generateProfilePictureUrls(ctx, journeys); err != nil {
        log.Printf("Warning: Failed to generate some presigned URLs for profile pictures: %v", err)
        // Continue with the journeys we have
    }

    // Transform journeys to response format
    journeysResponse := make([]JourneyResponse, len(journeys))
    for i, journey := range journeys {
        momentsResponse := make([]MomentResponse, len(journey.Moments))
        for j, moment := range journey.Moments {
            momentsResponse[j] = MomentResponse{
                Title:       moment.Title,
                Date:        moment.Date,
                Location:    moment.Location,
                ImageUrl: moment.ResourceKey,
            }
        }
        journeysResponse[i] = JourneyResponse{
            Username:      journey.Username,
            DateCreated:   journey.DateCreated,
            Title:         journey.Title,
            Description:   journey.Description,
            Hashtags:      journey.Hashtags,
            Moments:       momentsResponse,
            ProfilePicture: journey.ProfilePicture,
        }
    }

    // Convert journeys to JSON
    responseBody, err := json.Marshal(journeysResponse)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to serialize response: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers:    map[string]string{"Content-Type": "application/json"},
    }, nil
}

func main() {
    lambda.Start(handler)
}

func createPresignedUrl(ctx context.Context, key string, bucketName string, presignClient *s3.PresignClient) (string, error) {
    if key == "" {
        return "", nil
    }
    
    // Create the presigned URL with an expiration time of 1 hour
    presignedReq, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
        Bucket: aws.String(bucketName),
        Key:    aws.String(key),
    }, func(opts *s3.PresignOptions) {
        opts.Expires = 1 * time.Hour
    })

    log.Printf("Presigned URL: %s", presignedReq.URL)
    
    if err != nil {
        return "", fmt.Errorf("failed to create presigned URL: %v", err)
    }
    
    return presignedReq.URL, nil
}

// Generate presigned URLs for all resource keys
func generatePresignedUrls(ctx context.Context, journeys []Journey) error {
    bucketName := os.Getenv("JOURNEY_PHOTOS_BUCKET")
    if bucketName == "" {
        return fmt.Errorf("JOURNEY_PHOTOS_BUCKET environment variable not set")
    }

    // Create presigner
    presignClient := s3.NewPresignClient(s3Client)

    // Use a wait group to process all URLs in parallel
    var wg sync.WaitGroup
    
    // Create a mutex to protect concurrent map access
    var mu sync.Mutex
    
    // Map to store resource keys to presigned URLs
    urlMap := make(map[string]string)
    
    // Channel to collect errors
    errCh := make(chan error, 1)
    
    // Process each journey and its moments
    for i, journey := range journeys {
        for j, moment := range journey.Moments {
            wg.Add(1)
            
            // Generate presigned URL in a goroutine
            go func(resourceKey string, journeyIndex, momentIndex int) {
                defer wg.Done()

                url, err := createPresignedUrl(ctx, resourceKey, bucketName, presignClient)
                
                // Create the presigned URL with an expiration time of 1 hour
                // presignedReq, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
                //     Bucket: &bucketName,
                //     Key:    &resourceKey,
                // }, func(opts *s3.PresignOptions) {
                //     opts.Expires = 1 * time.Hour
                // })

                

                log.Printf("Url for %s: %s", resourceKey, url)
                
                if err != nil {
                    select {
                    case errCh <- fmt.Errorf("failed to create presigned URL for %s: %v", resourceKey, err):
                    default:
                        // If channel is full, just log the error
                        log.Printf("Error creating presigned URL for %s: %v", resourceKey, err)
                    }
                    return
                }
                
                // Store the URL in the map
                mu.Lock()
                // urlMap[resourceKey] = presignedReq.URL
                urlMap[resourceKey] = url
                mu.Unlock()
            }(moment.ResourceKey, i, j)
        }
    }
    
    // Wait for all goroutines to finish
    wg.Wait()
    
    // Check if there were any errors
    select {
    case err := <-errCh:
        return err
    default:
        // No errors
    }
    
    // Replace resource keys with presigned URLs
    for i := range journeys {
        for j := range journeys[i].Moments {
            if url, ok := urlMap[journeys[i].Moments[j].ResourceKey]; ok {
                // Replace with presigned URL
                journeys[i].Moments[j].ResourceKey = url
            }
        }
    }
    
    return nil
}

// Generate presigned URLs for profile pictures
func generateProfilePictureUrls(ctx context.Context, journeys []Journey) error {
    bucketName := os.Getenv("PROFILE_PICTURES_BUCKET")
    if bucketName == "" {
        return fmt.Errorf("PROFILE_PICTURES_BUCKET environment variable not set")
    }

    // Create presigner
    presignClient := s3.NewPresignClient(s3Client)

    // Use a wait group to process all URLs in parallel
    var wg sync.WaitGroup
    
    // Create a mutex to protect concurrent map access
    var mu sync.Mutex
    
    // Map to store usernames to presigned URLs
    urlMap := make(map[string]string)
    
    // Channel to collect errors
    errCh := make(chan error, 1)
    
    // Set of usernames we've already processed
    processedUsernames := make(map[string]bool)
    
    // Process each journey's username
    for _, journey := range journeys {
        username := journey.Username
        
        // Skip if we've already processed this username
        if processedUsernames[username] {
            continue
        }
        
        processedUsernames[username] = true
        wg.Add(1)
        
        // Generate presigned URL in a goroutine
        go func(username string) {
            defer wg.Done()

            // The standard profile picture key format
            resourceKey := fmt.Sprintf("standard/%s", username)
            
            url, err := createPresignedUrl(ctx, resourceKey, bucketName, presignClient)
            
            if err != nil {
                select {
                case errCh <- fmt.Errorf("failed to create presigned URL for profile picture of %s: %v", username, err):
                default:
                    // If channel is full, just log the error
                    log.Printf("Error creating presigned URL for profile picture of %s: %v", username, err)
                }
                return
            }
            
            // Store the URL in the map
            mu.Lock()
            urlMap[username] = url
            mu.Unlock()
        }(username)
    }
    
    // Wait for all goroutines to finish
    wg.Wait()
    
    // Check if there were any errors
    select {
    case err := <-errCh:
        return err
    default:
        // No errors
    }
    
    // Add profile picture URLs to journeys
    for i := range journeys {
        if url, ok := urlMap[journeys[i].Username]; ok {
            journeys[i].ProfilePicture = url
        }
    }
    
    return nil
}
