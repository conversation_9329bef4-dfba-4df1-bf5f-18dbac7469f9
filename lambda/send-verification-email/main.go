package main

import (
    "context"
    "fmt"
    "log"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/ses"
    "github.com/aws/aws-sdk-go-v2/service/ses/types"
)

var (
    sesClient *ses.Client
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    sesClient = ses.NewFromConfig(cfg)
}

func sendEmail(ctx context.Context, email, verificationCode string) error {
    subject := "Verify Email"
    htmlBody := fmt.Sprintf(`
        Your verification code is:
        <h1>%s</h1>
    `, verificationCode)

    input := &ses.SendEmailInput{
        Source: aws.String("<EMAIL>"),
        Destination: &types.Destination{
            ToAddresses: []string{email},
        },
        Message: &types.Message{
            Subject: &types.Content{
                Data: aws.String(subject),
            },
            Body: &types.Body{
                Html: &types.Content{
                    Data: aws.String(htmlBody),
                },
            },
        },
    }

    _, err := sesClient.SendEmail(ctx, input)
    return err
}

func handler(ctx context.Context, event events.DynamoDBEvent) error {
    for _, record := range event.Records {
        // Only process new records
        if record.EventName != "INSERT" {
            continue
        }

        // Extract email and verification code from the record
        email := record.Change.NewImage["email"].String()

        verificationCode := record.Change.NewImage["verificationCode"].String()

        // Send the email
        if err := sendEmail(ctx, email, verificationCode); err != nil {
            log.Printf("Error sending email to %s: %v", email, err)
            continue
        }

        log.Printf("Successfully sent verification email to %s", email)
    }

    return nil
}

func main() {
    lambda.Start(handler)
}
