package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
    cognitoTypes "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

type Request struct {
    SessionToken string `json:"sessionToken"`
    Password     string `json:"password"`
}

// type Response struct {
//     Message string `json:"message,omitempty"`
//     AuthData *AuthData `json:"authData,omitempty"`
// }

type Response struct {
    IdToken      string `json:"idToken"`
    AccessToken  string `json:"accessToken"`
    RefreshToken string `json:"refreshToken"`
}

type TempUser struct {
    Username    string
    Email       string
    PhoneNumber string
    CountryCode string
}

var (
    dynamoClient *dynamodb.Client
    cognitoClient *cognitoidentityprovider.Client
    userPoolId string
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
    cognitoClient = cognitoidentityprovider.NewFromConfig(cfg)
    userPoolId = os.Getenv("USER_POOL_ID")
}

func getTempUser(ctx context.Context, sessionToken string) (*TempUser, error) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        return nil, err
    }

    if len(result.Item) == 0 {
        return nil, nil
    }

    user := &TempUser{
        Username:    result.Item["username"].(*types.AttributeValueMemberS).Value,
        Email:       result.Item["email"].(*types.AttributeValueMemberS).Value,
        PhoneNumber: result.Item["phoneNumber"].(*types.AttributeValueMemberS).Value,
        CountryCode: result.Item["countryCode"].(*types.AttributeValueMemberS).Value,
    }

    return user, nil
}

func checkExistingUser(ctx context.Context, email, phoneNumber, countryCode string) (bool, error) {
    // Check email
    emailInput := &dynamodb.QueryInput{
        TableName: aws.String("user"),
        IndexName: aws.String("email-index"),
        KeyConditionExpression: aws.String("email = :email"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":email": &types.AttributeValueMemberS{Value: email},
        },
    }

    emailResult, err := dynamoClient.Query(ctx, emailInput)
    if err != nil {
        return false, err
    }

    if emailResult.Count > 0 {
        return true, nil
    }

    // Check phone number
    phoneInput := &dynamodb.QueryInput{
        TableName: aws.String("user"),
        IndexName: aws.String("phoneNumber-countryCode-index"),
        KeyConditionExpression: aws.String("phoneNumber = :phoneNumber AND countryCode = :countryCode"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":phoneNumber": &types.AttributeValueMemberS{Value: phoneNumber},
            ":countryCode": &types.AttributeValueMemberS{Value: countryCode},
        },
    }

    phoneResult, err := dynamoClient.Query(ctx, phoneInput)
    if err != nil {
        return false, err
    }

    return phoneResult.Count > 0, nil
}

func createCognitoUser(ctx context.Context, user *TempUser, password string) (*Response, error) {
    input := &cognitoidentityprovider.AdminCreateUserInput{
        UserPoolId: aws.String(userPoolId),
        Username:   aws.String(user.Username),
        UserAttributes: []cognitoTypes.AttributeType{
            {
                Name:  aws.String("email"),
                Value: aws.String(user.Email),
            },
            {
                Name:  aws.String("phone_number"),
                Value: aws.String(fmt.Sprintf("+%s%s", user.CountryCode, user.PhoneNumber)),
            },
            {
                Name:  aws.String("email_verified"),
                Value: aws.String("true"),
            },
            {
                Name:  aws.String("phone_number_verified"),
                Value: aws.String("true"),
            },
        },
        TemporaryPassword: aws.String(password),
    }

    _, err := cognitoClient.AdminCreateUser(ctx, input)
    if err != nil {
        return nil, err
    }

    // Set permanent password
    setPasswordInput := &cognitoidentityprovider.AdminSetUserPasswordInput{
        UserPoolId: aws.String(userPoolId),
        Username:   aws.String(user.Username),
        Password:   aws.String(password),
        Permanent:  true,
    }

    _, err = cognitoClient.AdminSetUserPassword(ctx, setPasswordInput)
    if err != nil {
        return nil, err
    }

    // Initiate auth session to get tokens
    authInput := &cognitoidentityprovider.AdminInitiateAuthInput{
        UserPoolId: aws.String(userPoolId),
        ClientId:   aws.String(os.Getenv("CLIENT_ID")), // You'll need to add this env var
        AuthFlow:   cognitoTypes.AuthFlowTypeAdminUserPasswordAuth,
        AuthParameters: map[string]string{
            "USERNAME": user.Username,
            "PASSWORD": password,
        },
    }

    authResult, err := cognitoClient.AdminInitiateAuth(ctx, authInput)
    if err != nil {
        return nil, err
    }

    return &Response{
        IdToken:      *authResult.AuthenticationResult.IdToken,
        AccessToken:  *authResult.AuthenticationResult.AccessToken,
        RefreshToken: *authResult.AuthenticationResult.RefreshToken,
    }, nil
}

func createUserInDynamoDB(ctx context.Context, user *TempUser) error {
    input := &dynamodb.PutItemInput{
        TableName: aws.String("user"),
        Item: map[string]types.AttributeValue{
            "username":    &types.AttributeValueMemberS{Value: user.Username},
            "email":       &types.AttributeValueMemberS{Value: user.Email},
            "phoneNumber": &types.AttributeValueMemberS{Value: user.PhoneNumber},
            "countryCode": &types.AttributeValueMemberS{Value: user.CountryCode},
        },
    }

    _, err := dynamoClient.PutItem(ctx, input)
    return err
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers: map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Get temp user
    tempUser, err := getTempUser(ctx, req.SessionToken)
    if err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error getting temp user: %v", err)
    }

    if tempUser == nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       `{"error": "Invalid session token"}`,
            Headers: map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Check if user exists
    exists, err := checkExistingUser(ctx, tempUser.Email, tempUser.PhoneNumber, tempUser.CountryCode)
    if err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking existing user: %v", err)
    }

    if exists {
        return events.APIGatewayProxyResponse{
            StatusCode: 409,
            Body:       `{"error": "User with this email or phone number already exists"}`,
            Headers: map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Create Cognito user
    authData, err := createCognitoUser(ctx, tempUser, req.Password)
    if err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error creating cognito user: %v", err)
    }

    // Create user in DynamoDB
    if err := createUserInDynamoDB(ctx, tempUser); err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error creating user in database: %v", err)
    }

    response := authData

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error marshaling response: %v", err)
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{"Content-Type": "application/json"},
    }, nil
}

func main() {
    lambda.Start(handler)
}
