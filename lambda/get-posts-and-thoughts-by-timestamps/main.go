package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client

func init() {
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		panic(fmt.Sprintf("Failed to load AWS config: %v", err))
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
}

// Request represents the incoming request
type Request struct {
	PostDateCreatedTimeStamps    []int64 `json:"postDateCreatedTimeStamps"`
	ThoughtDateCreatedTimeStamps []int64 `json:"thoughtDateCreatedTimeStamps"`
	Username                     string  `json:"username,omitempty"`
}

// Moment represents a single moment in a post
type Moment struct {
	Title       *string `json:"title,omitempty"`
	Date        *int64  `json:"date,omitempty"`
	Location    *string `json:"location,omitempty"`
	ResourceKey string  `json:"resourceKey"`
}

// Post represents a post record
type Post struct {
	Username     string   `json:"username"`
	DateCreated  int64    `json:"dateCreated"`
	Title        *string  `json:"title,omitempty"`
	Description  *string  `json:"description,omitempty"`
	Hashtags     []string `json:"hashtags,omitempty"`
	Moments      []Moment `json:"moments"`
}

// Thought represents a thought record
type Thought struct {
	Username           string `json:"username"`
	DateCreated        int64  `json:"dateCreated"`
	ThoughtBody        string `json:"thoughtBody"`
	Journey            *int   `json:"journey,omitempty"`
	NumberOfComments   int    `json:"numberOfComments"`
	// Add other thought fields as needed
}

// Response represents the API response
type Response struct {
	SortedTimestamps []int64            `json:"sortedTimestamps"`
	PostsMap         map[string]Post    `json:"postsMap"`
	ThoughtsMap      map[string]Thought `json:"thoughtsMap"`
}

// Extract username from JWT token
func extractUsername(authHeader string) (string, error) {
	if authHeader == "" {
		return "", fmt.Errorf("authorization header is required")
	}

	// Remove "Bearer " prefix
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == authHeader {
		return "", fmt.Errorf("invalid authorization header format")
	}

	// Parse token without verification (since we trust API Gateway's validation)
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	// Extract username from claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		if username, exists := claims["cognito:username"]; exists {
			if usernameStr, ok := username.(string); ok {
				return usernameStr, nil
			}
		}
	}

	return "", fmt.Errorf("username not found in token")
}

// Get posts by username and timestamps
func getPostsByTimestamps(ctx context.Context, username string, timestamps []int64) (map[string]Post, error) {
	if len(timestamps) == 0 {
		return make(map[string]Post), nil
	}

	tableName := os.Getenv("POST_TABLE_NAME")
	if tableName == "" {
		return nil, fmt.Errorf("POST_TABLE_NAME environment variable not set")
	}

	postsMap := make(map[string]Post)

	// Create batch get request
	var requestItems []map[string]types.AttributeValue
	for _, timestamp := range timestamps {
		requestItems = append(requestItems, map[string]types.AttributeValue{
			"username":     &types.AttributeValueMemberS{Value: username},
			"date_created": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", timestamp)},
		})
	}

	// Execute batch get item
	input := &dynamodb.BatchGetItemInput{
		RequestItems: map[string]types.KeysAndAttributes{
			tableName: {
				Keys: requestItems,
			},
		},
	}

	result, err := dynamoClient.BatchGetItem(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to get posts: %v", err)
	}

	// Process results
	for _, item := range result.Responses[tableName] {
		post := Post{}

		// Extract username
		if val, ok := item["username"].(*types.AttributeValueMemberS); ok {
			post.Username = val.Value
		}

		// Extract date_created
		if val, ok := item["date_created"].(*types.AttributeValueMemberN); ok {
			if dateCreated, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				post.DateCreated = dateCreated
			}
		}

		// Extract title (optional)
		if val, ok := item["title"].(*types.AttributeValueMemberS); ok {
			titleStr := val.Value
			post.Title = &titleStr
		}

		// Extract description (optional)
		if val, ok := item["description"].(*types.AttributeValueMemberS); ok {
			descStr := val.Value
			post.Description = &descStr
		}

		// Extract hashtags (optional)
		if val, ok := item["hashtags"].(*types.AttributeValueMemberS); ok {
			var hashtagList []string
			if err := json.Unmarshal([]byte(val.Value), &hashtagList); err == nil {
				post.Hashtags = hashtagList
			}
		}

		// Extract moments
		if val, ok := item["moments"].(*types.AttributeValueMemberS); ok {
			var momentList []Moment
			if err := json.Unmarshal([]byte(val.Value), &momentList); err == nil {
				post.Moments = momentList
			}
		}

		// Use date_created as key
		key := fmt.Sprintf("%d", post.DateCreated)
		postsMap[key] = post
	}

	return postsMap, nil
}

// Get thoughts by username and timestamps
func getThoughtsByTimestamps(ctx context.Context, username string, timestamps []int64) (map[string]Thought, error) {
	if len(timestamps) == 0 {
		return make(map[string]Thought), nil
	}

	tableName := os.Getenv("THOUGHT_TABLE_NAME")
	if tableName == "" {
		return nil, fmt.Errorf("THOUGHT_TABLE_NAME environment variable not set")
	}

	thoughtsMap := make(map[string]Thought)

	// Create batch get request
	var requestItems []map[string]types.AttributeValue
	for _, timestamp := range timestamps {
		requestItems = append(requestItems, map[string]types.AttributeValue{
			"username":     &types.AttributeValueMemberS{Value: username},
			"date_created": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", timestamp)},
		})
	}

	// Execute batch get item
	input := &dynamodb.BatchGetItemInput{
		RequestItems: map[string]types.KeysAndAttributes{
			tableName: {
				Keys: requestItems,
			},
		},
	}

	result, err := dynamoClient.BatchGetItem(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to get thoughts: %v", err)
	}

	// Process results
	for _, item := range result.Responses[tableName] {
		thought := Thought{}

		// Extract username
		if val, ok := item["username"].(*types.AttributeValueMemberS); ok {
			thought.Username = val.Value
		}

		// Extract date_created
		if val, ok := item["date_created"].(*types.AttributeValueMemberN); ok {
			if dateCreated, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				thought.DateCreated = dateCreated
			}
		}

		// Extract thought_body
		if val, ok := item["thought_body"].(*types.AttributeValueMemberS); ok {
			thought.ThoughtBody = val.Value
		}

		// Extract journey (optional)
		if val, ok := item["journey"].(*types.AttributeValueMemberN); ok {
			if journey, err := strconv.Atoi(val.Value); err == nil {
				thought.Journey = &journey
			}
		}

		// Extract number_of_comments
		if val, ok := item["number_of_comments"].(*types.AttributeValueMemberN); ok {
			if numberOfComments, err := strconv.Atoi(val.Value); err == nil {
				thought.NumberOfComments = numberOfComments
			}
		}

		// Use date_created as key
		key := fmt.Sprintf("%d", thought.DateCreated)
		thoughtsMap[key] = thought
	}

	return thoughtsMap, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req Request
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Determine username: use provided username or extract from token
	var username string
	var err error

	if req.Username != "" {
		username = req.Username
		// Still validate token for authorization
		_, err = extractUsername(request.Headers["Authorization"])
		if err != nil {
			return events.APIGatewayProxyResponse{
				StatusCode: 401,
				Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	} else {
		// Extract username from token
		username, err = extractUsername(request.Headers["Authorization"])
		if err != nil {
			return events.APIGatewayProxyResponse{
				StatusCode: 401,
				Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	// Get posts and thoughts concurrently
	postsMap, err := getPostsByTimestamps(ctx, username, req.PostDateCreatedTimeStamps)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get posts: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	thoughtsMap, err := getThoughtsByTimestamps(ctx, username, req.ThoughtDateCreatedTimeStamps)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get thoughts: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Combine and sort all timestamps
	var allTimestamps []int64
	allTimestamps = append(allTimestamps, req.PostDateCreatedTimeStamps...)
	allTimestamps = append(allTimestamps, req.ThoughtDateCreatedTimeStamps...)
	
	// Sort timestamps in descending order (newest first)
	sort.Slice(allTimestamps, func(i, j int) bool {
		return allTimestamps[i] > allTimestamps[j]
	})

	// Create response
	response := Response{
		SortedTimestamps: allTimestamps,
		PostsMap:         postsMap,
		ThoughtsMap:      thoughtsMap,
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
