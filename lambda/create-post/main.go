package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "os"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
    "github.com/aws/aws-sdk-go-v2/service/s3"
    "github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client
var s3Client *s3.Client

func init() {
    // Initialize the AWS SDK clients
    cfg, err := config.LoadDefaultConfig(context.TODO())
    if err != nil {
        log.Fatalf("unable to load SDK config, %v", err)
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
    s3Client = s3.NewFromConfig(cfg)
}

// Moment represents a single moment in a journey
type Moment struct {
    Title       *string `json:"title,omitempty"`
    Date        *string `json:"date,omitempty"`
    Location    *string `json:"location,omitempty"`
    ResourceKey string  `json:"resourceKey"`
}

// Journey represents a journey post
type Journey struct {
    Title       *string   `json:"title,omitempty"`
    Description *string   `json:"description,omitempty"`
    Hashtags    []string  `json:"hashtags,omitempty"`
    Moments     []Moment  `json:"moments"`
}

// Response represents the API response
type Response struct {
    DateCreated int64  `json:"dateCreated"`
    Message     string `json:"message"`
}

// ValidationError represents a validation error
type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

// Validate journey fields
func validateJourney(journey Journey) []ValidationError {
    var errors []ValidationError

    // Validate Journey fields
    if journey.Title != nil && len(*journey.Title) > 100 {
        errors = append(errors, ValidationError{
            Field:   "title",
            Message: "Title must not exceed 100 characters",
        })
    }

    if journey.Description != nil && len(*journey.Description) > 250 {
        errors = append(errors, ValidationError{
            Field:   "description",
            Message: "Description must not exceed 250 characters",
        })
    }

    // Validate hashtags
    for i, hashtag := range journey.Hashtags {
        if len(hashtag) > 20 {
            errors = append(errors, ValidationError{
                Field:   fmt.Sprintf("hashtags[%d]", i),
                Message: "Hashtag must not exceed 20 characters",
            })
        }
    }

    // Validate Moment fields
    for i, moment := range journey.Moments {
        if moment.Title != nil && len(*moment.Title) > 100 {
            errors = append(errors, ValidationError{
                Field:   fmt.Sprintf("moments[%d].title", i),
                Message: "Moment title must not exceed 100 characters",
            })
        }

        if moment.Location != nil && len(*moment.Location) > 20 {
            errors = append(errors, ValidationError{
                Field:   fmt.Sprintf("moments[%d].location", i),
                Message: "Moment location must not exceed 20 characters",
            })
        }
    }

    return errors
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
    if tokenString == "" {
        return "", fmt.Errorf("no token provided")
    }

    // Remove "Bearer " prefix if present
    if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
        tokenString = tokenString[7:]
    }

    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
        return "", fmt.Errorf("failed to parse token: %v", err)
    }

    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
        return "", fmt.Errorf("failed to extract claims")
    }

    // Extract username from token claims
    username, ok := claims["cognito:username"].(string)
    if !ok {
        return "", fmt.Errorf("username not found in token")
    }

    return username, nil
}

// Check if an object exists in S3
func objectExists(ctx context.Context, bucketName, key string) (bool, error) {
    _, err := s3Client.HeadObject(ctx, &s3.HeadObjectInput{
        Bucket: &bucketName,
        Key:    &key,
    })
    
    if err != nil {
        // Check if the error is because the object doesn't exist
        // This is a bit tricky with AWS SDK v2, as it doesn't expose the status code directly
        // We'll check if the error message contains "NotFound"
        if err.Error() == "NotFound" {
            return false, nil
        }
        return false, fmt.Errorf("error checking if object exists: %v", err)
    }
    
    return true, nil
}

// Verify that all moment resources exist in S3
func verifyMomentResources(ctx context.Context, bucketName string, moments []Moment) error {
    for i, moment := range moments {
        exists, err := objectExists(ctx, bucketName, moment.ResourceKey)
        if err != nil {
            return fmt.Errorf("error verifying resource for moment %d: %v", i, err)
        }
        
        if !exists {
            return fmt.Errorf("resource for moment %d with key '%s' does not exist in bucket '%s'", 
                i, moment.ResourceKey, bucketName)
        }
    }
    
    return nil
}

// Save journey to DynamoDB
func saveJourney(ctx context.Context, username string, journey Journey) (int64, error) {    
    // Get current timestamp in seconds
    dateCreated := time.Now().Unix()
    
    // Convert moments to DynamoDB attribute value
    momentsJSON, err := json.Marshal(journey.Moments)
    if err != nil {
        return 0, fmt.Errorf("failed to marshal moments: %v", err)
    }
    
    // Convert hashtags to DynamoDB attribute value
    hashtagsJSON, err := json.Marshal(journey.Hashtags)
    if err != nil {
        return 0, fmt.Errorf("failed to marshal hashtags: %v", err)
    }
    
    // Create item for DynamoDB
    item := map[string]types.AttributeValue{
        "username":     &types.AttributeValueMemberS{Value: username},
        "date_created": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", dateCreated)},
        "moments":      &types.AttributeValueMemberS{Value: string(momentsJSON)},
    }
    
    // Add optional fields if they exist
    if journey.Title != nil {
        item["title"] = &types.AttributeValueMemberS{Value: *journey.Title}
    }
    
    if journey.Description != nil {
        item["description"] = &types.AttributeValueMemberS{Value: *journey.Description}
    }
    
    if len(journey.Hashtags) > 0 {
        item["hashtags"] = &types.AttributeValueMemberS{Value: string(hashtagsJSON)}
    }
    
    // Get table name from environment variable
    tableName := os.Getenv("JOURNEY_TABLE_NAME")
    if tableName == "" {
        return 0, fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
    }
    
    // Put item in DynamoDB
    _, err = dynamoClient.PutItem(ctx, &dynamodb.PutItemInput{
        TableName: &tableName,
        Item:      item,
    })
    
    if err != nil {
        return 0, fmt.Errorf("failed to save journey: %v", err)
    }
    
    return dateCreated, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Extract username from token
    username, err := extractUsername(request.Headers["Authorization"])
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Parse request body
    var journey Journey
    if err := json.Unmarshal([]byte(request.Body), &journey); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Log the journey for debugging
    log.Printf("Received journey from user %s: %+v", username, journey)

    // Validate journey fields
    if validationErrors := validateJourney(journey); len(validationErrors) > 0 {
        errorResponse, _ := json.Marshal(map[string]interface{}{
            "error":  "Validation failed",
            "errors": validationErrors,
        })
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       string(errorResponse),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Get bucket name from environment variable
    bucketName := os.Getenv("JOURNEY_PHOTOS_BUCKET")
    if bucketName == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "JOURNEY_PHOTOS_BUCKET environment variable not set"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Verify that all moment resources exist in S3
    if err := verifyMomentResources(ctx, bucketName, journey.Moments); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       fmt.Sprintf(`{"error": "Resource verification failed: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Save journey to DynamoDB
    _, err = saveJourney(ctx, username, journey)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to save journey: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 201, // Created
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}
