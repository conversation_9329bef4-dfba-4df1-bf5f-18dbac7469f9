package main

import (
    "context"
    "fmt"
    "os"
    "strings"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

func handler(ctx context.Context, s3Event events.S3Event) error {
    // Get the bucket and key from the event
    record := s3Event.Records[0]
    key := record.S3.Object.Key

    // Extract username and type from the key (format: username/[standard|small]/timestamp)
    parts := strings.Split(key, "/")
    if len(parts) != 2 {
        return fmt.Errorf("invalid key format: %s", key)
    }
    
    username := parts[1]
    imageType := parts[0]

    // Determine which attribute to update based on the image type
    var updateExpression string
    var expressionAttributeNames map[string]string
    
    switch imageType {
    case "standard":
        updateExpression = "SET #pp = :pic"
        expressionAttributeNames = map[string]string{
            "#pp": "profilePicture",
        }
    case "small":
        updateExpression = "SET #pps = :pic"
        expressionAttributeNames = map[string]string{
            "#pps": "profilePictureSmall",
        }
    default:
        return fmt.Errorf("invalid image type in path: %s", imageType)
    }

    // Update DynamoDB
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        return fmt.Errorf("unable to load SDK config: %v", err)
    }

    dynamoClient := dynamodb.NewFromConfig(cfg)
    
    input := &dynamodb.UpdateItemInput{
        TableName: aws.String(os.Getenv("USER_TABLE_NAME")),
        Key: map[string]types.AttributeValue{
            "username": &types.AttributeValueMemberS{Value: username},
        },
        UpdateExpression: aws.String(updateExpression),
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":pic": &types.AttributeValueMemberS{Value: key},
        },
    }

    _, err = dynamoClient.UpdateItem(ctx, input)
    if err != nil {
        return fmt.Errorf("failed to update DynamoDB: %v", err)
    }

    return nil
}

func main() {
    lambda.Start(handler)
}
