package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"
    "strings"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
    "github.com/golang-jwt/jwt/v5"
    "github.com/aws/aws-sdk-go-v2/aws"

)

type Response struct {
    Username        string `json:"username"`
    FirstName       string `json:"firstName"`
    LastName        string `json:"lastName"`
    Bio             string `json:"bio"`
    CurrentLocation string `json:"currentLocation"`
}

type Request struct {
    FirstName       string `json:"firstName"`
    LastName        string `json:"lastName"`
    Bio            string `json:"bio"`
    CurrentLocation string `json:"currentLocation"`
}

type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

var (
    dynamoClient *dynamodb.Client
    userTableName string
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
    userTableName = os.Getenv("USER_TABLE_NAME")
}

func validateRequest(req Request) []ValidationError {
    var errors []ValidationError

    if len(req.FirstName) > 100 {
        errors = append(errors, ValidationError{
            Field:   "firstName",
            Message: "First name must not exceed 100 characters",
        })
    }

    if len(req.LastName) > 100 {
        errors = append(errors, ValidationError{
            Field:   "lastName",
            Message: "Last name must not exceed 100 characters",
        })
    }

    if len(req.Bio) > 250 {
        errors = append(errors, ValidationError{
            Field:   "bio",
            Message: "Bio must not exceed 250 characters",
        })
    }

    if len(req.CurrentLocation) > 100 {
        errors = append(errors, ValidationError{
            Field:   "currentLocation",
            Message: "Current location must not exceed 100 characters",
        })
    }

    return errors
}

func extractUsername(tokenString string) (string, error) {
    // Remove "Bearer " prefix if present
    tokenString = strings.TrimPrefix(tokenString, "Bearer ")
    
    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
        return "", fmt.Errorf("failed to parse token: %v", err)
    }

    if claims, ok := token.Claims.(jwt.MapClaims); ok {
        if username, ok := claims["cognito:username"].(string); ok {
            return username, nil
        }
    }
    return "", fmt.Errorf("username not found in token claims")
}

func updateUserProfile(ctx context.Context, username string, req Request) error {
    input := &dynamodb.UpdateItemInput{
        TableName: &userTableName,
        Key: map[string]types.AttributeValue{
            "username": &types.AttributeValueMemberS{Value: username},
        },
        UpdateExpression: aws.String("SET firstName = :fn, lastName = :ln, bio = :b, currentLocation = :cl"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":fn": &types.AttributeValueMemberS{Value: req.FirstName},
            ":ln": &types.AttributeValueMemberS{Value: req.LastName},
            ":b":  &types.AttributeValueMemberS{Value: req.Bio},
            ":cl": &types.AttributeValueMemberS{Value: req.CurrentLocation},
        },
    }

    _, err := dynamoClient.UpdateItem(ctx, input)
    return err
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Extract authorization token
    authHeader := request.Headers["Authorization"]
    if authHeader == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       `{"error": "Missing authorization token"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Extract username from token
    username, err := extractUsername(authHeader)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Validate request
    if errors := validateRequest(req); len(errors) > 0 {
        errorResponse, _ := json.Marshal(map[string]interface{}{
            "error":  "Validation failed",
            "errors": errors,
        })
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       string(errorResponse),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Update user profile
    if err := updateUserProfile(ctx, username, req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to update profile: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    response := Response{
        Username:        username,
        FirstName:       req.FirstName,
        LastName:        req.LastName,
        Bio:            req.Bio,
        CurrentLocation: req.CurrentLocation,
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Failed to create response"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }   

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers:    map[string]string{"Content-Type": "application/json"},
    }, nil
}

func main() {
    lambda.Start(handler)
}