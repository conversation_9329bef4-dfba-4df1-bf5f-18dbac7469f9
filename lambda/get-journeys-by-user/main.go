package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client
var s3Client *s3.Client

func init() {
	// Initialize the AWS SDK clients
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
	s3Client = s3.NewFromConfig(cfg)
}

// Journey represents a journey record
type Journey struct {
	Username                       string `json:"username"`
	DateCreated                    int64  `json:"dateCreated"`
	Title                          string `json:"title,omitempty"`
	StartDate                      *int64 `json:"startDate,omitempty"`
	EndDate                        *int64 `json:"endDate,omitempty"`
	CoverPhoto                     string `json:"coverPhoto,omitempty"`
	UserProfilePicturePresignedUrl string `json:"userProfilePicturePresignedUrl,omitempty"`
}

// Response represents the API response
type Response struct {
	Journeys []Journey `json:"journeys"`
	Count    int       `json:"count"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
	if tokenString == "" {
		return "", fmt.Errorf("no token provided")
	}

	// Remove "Bearer " prefix if present
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("failed to extract claims")
	}

	// Extract username from token claims
	username, ok := claims["cognito:username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token")
	}

	return username, nil
}

// Get user profile picture from user table
func getUserProfilePicture(ctx context.Context, username string) (string, error) {
	userTableName := os.Getenv("USER_TABLE_NAME")
	if userTableName == "" {
		return "", fmt.Errorf("USER_TABLE_NAME environment variable not set")
	}

	// Get user item from DynamoDB
	result, err := dynamoClient.GetItem(ctx, &dynamodb.GetItemInput{
		TableName: &userTableName,
		Key: map[string]types.AttributeValue{
			"username": &types.AttributeValueMemberS{Value: username},
		},
	})

	if err != nil {
		return "", fmt.Errorf("failed to get user profile: %v", err)
	}

	// Extract profilePicture attribute if it exists
	if result.Item != nil {
		if val, ok := result.Item["profilePicture"].(*types.AttributeValueMemberS); ok {
			return val.Value, nil
		}
	}

	// Return empty string if no profile picture found
	return "", nil
}

// Generate presigned URL for user profile picture
func generateProfilePicturePresignedUrl(ctx context.Context, profilePictureKey string) (string, error) {
	if profilePictureKey == "" {
		return "", nil
	}

	bucketName := os.Getenv("PROFILE_PICTURES_BUCKET")
	if bucketName == "" {
		return "", fmt.Errorf("PROFILE_PICTURES_BUCKET environment variable not set")
	}

	// Create presigned URL request
	presigner := s3.NewPresignClient(s3Client)
	request, err := presigner.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &profilePictureKey,
	}, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(15 * time.Minute) // URL expires in 15 minutes
	})

	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %v", err)
	}

	return request.URL, nil
}

// Generate presigned URL for journey cover photo
func generateCoverPhotoPresignedUrl(ctx context.Context, coverPhotoKey string) (string, error) {
	if coverPhotoKey == "" {
		return "", nil
	}

	bucketName := os.Getenv("JOURNEY_COVER_PHOTOS_BUCKET")
	if bucketName == "" {
		return "", fmt.Errorf("JOURNEY_COVER_PHOTOS_BUCKET environment variable not set")
	}

	// Create presigned URL request
	presigner := s3.NewPresignClient(s3Client)
	request, err := presigner.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &coverPhotoKey,
	}, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(15 * time.Minute) // URL expires in 15 minutes
	})

	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %v", err)
	}

	return request.URL, nil
}

// Check if a journey should be included based on date filtering logic
func shouldIncludeJourney(journeyStartDate, journeyEndDate, queryStartDate, queryEndDate *int64) bool {
	// If journey has no start date, it's automatically included
	if journeyStartDate == nil {
		return true
	}

	// If journey has no end date, check if query dates are equal or after journey start date
	if journeyEndDate == nil {
		// Query start date must be equal or after journey start date
		if queryStartDate != nil && *queryStartDate < *journeyStartDate {
			return false
		}
		// Query end date must be equal or after journey start date
		if queryEndDate != nil && *queryEndDate < *journeyStartDate {
			return false
		}
		return true
	}

	// Journey has both start and end dates
	// Query dates must be equal or in between journey start and end dates
	if queryStartDate != nil {
		if *queryStartDate < *journeyStartDate || *queryStartDate > *journeyEndDate {
			return false
		}
	}
	if queryEndDate != nil {
		if *queryEndDate < *journeyStartDate || *queryEndDate > *journeyEndDate {
			return false
		}
	}

	return true
}

// Get journeys by username with optional date filtering
func getJourneysByUser(ctx context.Context, username string, startDate, endDate *int64) ([]Journey, error) {
	tableName := os.Getenv("JOURNEY_TABLE_NAME")
	if tableName == "" {
		return nil, fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
	}

	// Build the key condition expression using only the partition key
	keyCond := expression.Key("username").Equal(expression.Value(username))

	// Build the expression
	expr, err := expression.NewBuilder().
		WithKeyCondition(keyCond).
		Build()

	if err != nil {
		return nil, fmt.Errorf("failed to build expression: %v", err)
	}

	// Create query input
	input := &dynamodb.QueryInput{
		TableName:                 &tableName,
		KeyConditionExpression:    expr.KeyCondition(),
		ExpressionAttributeNames:  expr.Names(),
		ExpressionAttributeValues: expr.Values(),
		ScanIndexForward:          new(bool), // false = descending order (newest first)
	}

	// Execute query
	result, err := dynamoClient.Query(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to query journeys: %v", err)
	}

	// First, filter items by date logic before building Journey objects
	var filteredItems []map[string]types.AttributeValue
	for _, item := range result.Items {
		// Extract dates from item for filtering
		var itemStartDate, itemEndDate *int64

		if val, ok := item["start_date"].(*types.AttributeValueMemberN); ok {
			if startDate, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				itemStartDate = &startDate
			}
		}

		if val, ok := item["end_date"].(*types.AttributeValueMemberN); ok {
			if endDate, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				itemEndDate = &endDate
			}
		}

		// Apply date filtering logic
		if shouldIncludeJourney(itemStartDate, itemEndDate, startDate, endDate) {
			filteredItems = append(filteredItems, item)
		}
	}

	// Now build Journey objects only for filtered items
	var journeys []Journey
	for _, item := range filteredItems {
		journey := Journey{}

		// Extract username
		if val, ok := item["username"].(*types.AttributeValueMemberS); ok {
			journey.Username = val.Value
		}

		// Extract date_created
		if val, ok := item["date_created"].(*types.AttributeValueMemberN); ok {
			if dateCreated, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				journey.DateCreated = dateCreated
			}
		}

		// Extract title
		if val, ok := item["title"].(*types.AttributeValueMemberS); ok {
			journey.Title = val.Value
		}

		// Extract start_date
		if val, ok := item["start_date"].(*types.AttributeValueMemberN); ok {
			if startDate, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				journey.StartDate = &startDate
			}
		}

		// Extract end_date
		if val, ok := item["end_date"].(*types.AttributeValueMemberN); ok {
			if endDate, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				journey.EndDate = &endDate
			}
		}

		// Extract cover_photo_resource_key and generate presigned URL
		if val, ok := item["cover_photo_resource_key"].(*types.AttributeValueMemberS); ok {
			coverPhotoUrl, err := generateCoverPhotoPresignedUrl(ctx, val.Value)
			if err != nil {
				return nil, fmt.Errorf("failed to generate cover photo URL: %v", err)
			}
			journey.CoverPhoto = coverPhotoUrl
		}

		journeys = append(journeys, journey)
	}

	return journeys, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Determine username: check query parameter first, then extract from token
	var username string
	var err error

	// Check if username is provided as a query parameter
	if usernameParam, ok := request.QueryStringParameters["username"]; ok && usernameParam != "" {
		username = usernameParam

		// Still need to validate the token for authorization
		_, err = extractUsername(request.Headers["Authorization"])
		if err != nil {
			return events.APIGatewayProxyResponse{
				StatusCode: 401,
				Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	} else {
		// Extract username from token if not provided as query parameter
		username, err = extractUsername(request.Headers["Authorization"])
		if err != nil {
			return events.APIGatewayProxyResponse{
				StatusCode: 401,
				Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	// Parse optional query parameters
	var startDate, endDate *int64

	if startDateStr, ok := request.QueryStringParameters["startDate"]; ok && startDateStr != "" {
		if parsed, err := strconv.ParseInt(startDateStr, 10, 64); err == nil {
			startDate = &parsed
		} else {
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       `{"error": "Invalid startDate format. Must be a Unix timestamp"}`,
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	if endDateStr, ok := request.QueryStringParameters["endDate"]; ok && endDateStr != "" {
		if parsed, err := strconv.ParseInt(endDateStr, 10, 64); err == nil {
			endDate = &parsed
		} else {
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       `{"error": "Invalid endDate format. Must be a Unix timestamp"}`,
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	// Get user profile picture
	profilePictureKey, err := getUserProfilePicture(ctx, username)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get user profile: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Generate presigned URL for profile picture (only once for all journeys)
	profilePicturePresignedUrl, err := generateProfilePicturePresignedUrl(ctx, profilePictureKey)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to generate profile picture URL: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Get journeys
	journeys, err := getJourneysByUser(ctx, username, startDate, endDate)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get journeys: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Add the profile picture presigned URL to all journeys
	for i := range journeys {
		journeys[i].UserProfilePicturePresignedUrl = profilePicturePresignedUrl
	}

	// Create response
	response := Response{
		Journeys: journeys,
		Count:    len(journeys),
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
