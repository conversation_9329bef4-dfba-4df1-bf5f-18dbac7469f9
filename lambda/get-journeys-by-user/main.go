package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client

func init() {
	// Initialize the AWS SDK clients
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
}

// Journey represents a journey record
type Journey struct {
	Username              string `json:"username"`
	DateCreated           int64  `json:"dateCreated"`
	Title                 string `json:"title,omitempty"`
	StartDate             *int64 `json:"startDate,omitempty"`
	EndDate               *int64 `json:"endDate,omitempty"`
	CoverPhotoResourceKey string `json:"coverPhotoResourceKey,omitempty"`
}

// Response represents the API response
type Response struct {
	Journeys []Journey `json:"journeys"`
	Count    int       `json:"count"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
	if tokenString == "" {
		return "", fmt.Errorf("no token provided")
	}

	// Remove "Bearer " prefix if present
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("failed to extract claims")
	}

	// Extract username from token claims
	username, ok := claims["cognito:username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token")
	}

	return username, nil
}

// Get journeys by username with optional date filtering
func getJourneysByUser(ctx context.Context, username string, startDate, endDate *int64) ([]Journey, error) {
	tableName := os.Getenv("JOURNEY_TABLE_NAME")
	if tableName == "" {
		return nil, fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
	}

	// Build the key condition expression with date filtering on sort key
	var keyCond expression.KeyConditionBuilder

	if startDate != nil && endDate != nil {
		// Both start and end dates provided
		keyCond = expression.Key("username").Equal(expression.Value(username)).
			And(expression.Key("date_created").Between(expression.Value(*startDate), expression.Value(*endDate)))
	} else if startDate != nil {
		// Only start date provided
		keyCond = expression.Key("username").Equal(expression.Value(username)).
			And(expression.Key("date_created").GreaterThanEqual(expression.Value(*startDate)))
	} else if endDate != nil {
		// Only end date provided
		keyCond = expression.Key("username").Equal(expression.Value(username)).
			And(expression.Key("date_created").LessThanEqual(expression.Value(*endDate)))
	} else {
		// No date filtering
		keyCond = expression.Key("username").Equal(expression.Value(username))
	}

	// Build the expression
	expr, err := expression.NewBuilder().
		WithKeyCondition(keyCond).
		Build()

	if err != nil {
		return nil, fmt.Errorf("failed to build expression: %v", err)
	}

	// Create query input
	input := &dynamodb.QueryInput{
		TableName:                 &tableName,
		KeyConditionExpression:    expr.KeyCondition(),
		ExpressionAttributeNames:  expr.Names(),
		ExpressionAttributeValues: expr.Values(),
		ScanIndexForward:          new(bool), // false = descending order (newest first)
	}

	// Execute query
	result, err := dynamoClient.Query(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to query journeys: %v", err)
	}

	// Parse results
	var journeys []Journey
	for _, item := range result.Items {
		journey := Journey{}

		// Extract username
		if val, ok := item["username"].(*types.AttributeValueMemberS); ok {
			journey.Username = val.Value
		}

		// Extract date_created
		if val, ok := item["date_created"].(*types.AttributeValueMemberN); ok {
			if dateCreated, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				journey.DateCreated = dateCreated
			}
		}

		// Extract title
		if val, ok := item["title"].(*types.AttributeValueMemberS); ok {
			journey.Title = val.Value
		}

		// Extract start_date
		if val, ok := item["start_date"].(*types.AttributeValueMemberN); ok {
			if startDate, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				journey.StartDate = &startDate
			}
		}

		// Extract end_date
		if val, ok := item["end_date"].(*types.AttributeValueMemberN); ok {
			if endDate, err := strconv.ParseInt(val.Value, 10, 64); err == nil {
				journey.EndDate = &endDate
			}
		}

		// Extract cover_photo_resource_key
		if val, ok := item["cover_photo_resource_key"].(*types.AttributeValueMemberS); ok {
			journey.CoverPhotoResourceKey = val.Value
		}

		journeys = append(journeys, journey)
	}

	return journeys, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Extract username from token for authorization
	_, err := extractUsername(request.Headers["Authorization"])
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 401,
			Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Get username from path parameter
	username, ok := request.PathParameters["username"]
	if !ok || username == "" {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error": "Username path parameter is required"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Parse optional query parameters
	var startDate, endDate *int64

	if startDateStr, ok := request.QueryStringParameters["startDate"]; ok && startDateStr != "" {
		if parsed, err := strconv.ParseInt(startDateStr, 10, 64); err == nil {
			startDate = &parsed
		} else {
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       `{"error": "Invalid startDate format. Must be a Unix timestamp"}`,
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	if endDateStr, ok := request.QueryStringParameters["endDate"]; ok && endDateStr != "" {
		if parsed, err := strconv.ParseInt(endDateStr, 10, 64); err == nil {
			endDate = &parsed
		} else {
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       `{"error": "Invalid endDate format. Must be a Unix timestamp"}`,
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	// Get journeys
	journeys, err := getJourneysByUser(ctx, username, startDate, endDate)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get journeys: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Create response
	response := Response{
		Journeys: journeys,
		Count:    len(journeys),
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
