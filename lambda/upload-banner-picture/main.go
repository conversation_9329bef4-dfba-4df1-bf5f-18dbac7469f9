package main

import (
    "bytes"
    "context"
    "encoding/base64"
    "encoding/json"
    "fmt"
    "image"
    "image/jpeg"
    "image/png"
    "log"
    "os"
    "strings"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/s3"
    "github.com/golang-jwt/jwt/v5"
    "github.com/nfnt/resize"
)

const (
    BANNER_WIDTH  = 1080
)

var s3Client *s3.Client

type Response struct {
    Username           string `json:"username,omitempty"`
    BannerPicture      string `json:"bannerPicture,omitempty"`
}

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    s3Client = s3.NewFromConfig(cfg)
}

func extractUsername(tokenString string) (string, error) {
    tokenString = strings.TrimPrefix(tokenString, "Bearer ")
    
    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
        return "", fmt.Errorf("failed to parse token: %v", err)
    }

    if claims, ok := token.Claims.(jwt.MapClaims); ok {
        if username, ok := claims["cognito:username"].(string); ok {
            return username, nil
        }
    }
    return "", fmt.Errorf("username not found in token claims")
}

func decodeImage(data []byte, contentType string) (image.Image, error) {
    reader := bytes.NewReader(data)
    
    switch contentType {
    case "image/jpeg":
        return jpeg.Decode(reader)
    case "image/png":
        return png.Decode(reader)
    default:
        return nil, fmt.Errorf("unsupported image format: %s", contentType)
    }
}

func resizeImage(img image.Image) (image.Image, error) {
    // Calculate original width-to-height ratio
    bounds := img.Bounds()
    originalWidth := bounds.Dx()
    originalHeight := bounds.Dy()
    originalHeightToWidthRatio := float64(originalHeight) /float64(originalWidth)

    bannerHeight := uint(BANNER_WIDTH * originalHeightToWidthRatio)
    
    // Log the original dimensions and ratio
    log.Printf("Original dimensions: width: %dx height: %d, ratio: %f", originalWidth, originalHeight, originalHeightToWidthRatio)
    
    // Resize the image to the banner dimensions
    return resize.Resize(BANNER_WIDTH, bannerHeight, img, resize.Lanczos3), nil
}

func encodeImage(img image.Image, contentType string) ([]byte, error) {
    var buf bytes.Buffer
    
    switch contentType {
    case "image/jpeg":
        if err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 85}); err != nil {
            return nil, err
        }
    case "image/png":
        if err := png.Encode(&buf, img); err != nil {
            return nil, err
        }
    default:
        return nil, fmt.Errorf("unsupported image format: %s", contentType)
    }
    
    return buf.Bytes(), nil
}

func uploadToS3(ctx context.Context, data []byte, bucket, key, contentType string) error {
    _, err := s3Client.PutObject(ctx, &s3.PutObjectInput{
        Bucket:      &bucket,
        Key:         &key,
        Body:        bytes.NewReader(data),
        ContentType: &contentType,
    })
    return err
}

func createPresignedUrl(ctx context.Context, bucket string, key string) (string, error) {
    // Create presigner
    presignClient := s3.NewPresignClient(s3Client)
    
    // Create the presigned URL with an expiration time of 1 hour
    presignedReq, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
        Bucket: &bucket,
        Key:    &key,
    }, func(opts *s3.PresignOptions) {
        opts.Expires = time.Hour
    })
    
    if err != nil {
        return "", fmt.Errorf("failed to create presigned URL: %v", err)
    }
    
    return presignedReq.URL, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Println("Received request:", request)

    // Extract username from token
    username, err := extractUsername(request.Headers["Authorization"])
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
        }, nil
    }

    // Get content type and image data
    contentType := request.Headers["content-type"]
    if !strings.HasPrefix(contentType, "image/") {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid content type. Only images are allowed"}`,
        }, nil
    }

    // Decode base64 image data
    imageData, err := base64.StdEncoding.DecodeString(request.Body)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid image data"}`,
        }, nil
    }

    // Decode the image
    originalImage, err := decodeImage(imageData, contentType)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       fmt.Sprintf(`{"error": "Failed to decode image: %v"}`, err),
        }, nil
    }

    // Resize image to banner dimensions
    bannerImage, err := resizeImage(originalImage)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to resize image: %v"}`, err),
        }, nil
    }

    // Encode banner image
    bannerImageData, err := encodeImage(bannerImage, contentType)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to encode banner image: %v"}`, err),
        }, nil
    }

    bucket := os.Getenv("BUCKET_NAME")
    if bucket == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "BUCKET_NAME environment variable not set"}`,
        }, nil
    }

    // Upload banner image
    bannerKey := fmt.Sprintf("banner/%s", username)
    if err := uploadToS3(ctx, bannerImageData, bucket, bannerKey, contentType); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to upload banner image: %v"}`, err),
        }, nil
    }

    // Create presigned URL for the banner image
    bannerUrl, err := createPresignedUrl(ctx, bucket, bannerKey)
    if err != nil {
        log.Printf("Warning: Failed to create presigned URL for banner image: %v", err)
    }

    // Create response
    response := Response{
        Username:      username,
        BannerPicture: bannerUrl,
    }

    // Convert response to JSON
    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to create response: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}
