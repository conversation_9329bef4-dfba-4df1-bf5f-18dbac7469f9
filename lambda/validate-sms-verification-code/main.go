package main

import (
    "context"
    "encoding/json"
    "fmt"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

// Request represents the expected JSON payload
type Request struct {
    PhoneNumber      string `json:"phoneNumber"`
    CountryCode      string `json:"countryCode"`
    VerificationCode string `json:"verificationCode"`
    SessionToken     string `json:"sessionToken"`
}

// Response represents the Lambda response structure
type Response struct {
    PhoneNumber      string `json:"phoneNumber"`
    CountryCode      string `json:"countryCode"`
}

type CheckResult struct {
    Exists bool
    Error  error
}

var (
    dynamoClient *dynamodb.Client
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
}

func checkSessionToken(ctx context.Context, sessionToken string, results chan<- CheckResult) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        results <- CheckResult{Exists: false, Error: err}
        return
    }

    results <- CheckResult{Exists: len(result.Item) > 0, Error: nil}
}

func checkVerificationCode(ctx context.Context, phoneNumber, countryCode, code string, results chan<- CheckResult) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String("phone_number_verification_table"),
        Key: map[string]types.AttributeValue{
            "phoneNumber":      &types.AttributeValueMemberS{Value: phoneNumber},
            "verificationCode": &types.AttributeValueMemberS{Value: code},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        results <- CheckResult{Exists: false, Error: err}
        return
    }

    // Check if the item exists and has matching countryCode
    if len(result.Item) > 0 {
        if storedCountryCode, ok := result.Item["countryCode"].(*types.AttributeValueMemberS); ok {
            results <- CheckResult{Exists: storedCountryCode.Value == countryCode, Error: nil}
            return
        }
    }

    results <- CheckResult{Exists: false, Error: nil}
}

func updateUserSignUpTemp(ctx context.Context, sessionToken, phoneNumber, countryCode string) error {
    input := &dynamodb.UpdateItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
        UpdateExpression: aws.String("SET phoneNumber = :phoneNumber, countryCode = :countryCode"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":phoneNumber": &types.AttributeValueMemberS{Value: phoneNumber},
            ":countryCode": &types.AttributeValueMemberS{Value: countryCode},
        },
    }

    _, err := dynamoClient.UpdateItem(ctx, input)
    return err
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Validate request
    if req.PhoneNumber == "" || req.CountryCode == "" || req.VerificationCode == "" || req.SessionToken == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "PhoneNumber, countryCode, verificationCode, and sessionToken are required"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Create channels for results
    sessionResults := make(chan CheckResult, 1)
    verificationResults := make(chan CheckResult, 1)

    // Start parallel checks
    go checkSessionToken(ctx, req.SessionToken, sessionResults)
    go checkVerificationCode(ctx, req.PhoneNumber, req.CountryCode, req.VerificationCode, verificationResults)

    // Wait for both results
    sessionResult := <-sessionResults
    verificationResult := <-verificationResults

    // Check for errors
    if sessionResult.Error != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking session: %v", sessionResult.Error)
    }
    if verificationResult.Error != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking verification code: %v", verificationResult.Error)
    }

    // Check session token first (401 takes precedence)
    if !sessionResult.Exists {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       `{"error": "Invalid session token"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Then check verification code
    if !verificationResult.Exists {
        return events.APIGatewayProxyResponse{
            StatusCode: 404,
            Body:       `{"error": "Invalid verification code"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Update user_sign_up_temp table with the phone number and country code
    if err := updateUserSignUpTemp(ctx, req.SessionToken, req.PhoneNumber, req.CountryCode); err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error updating temp user: %v", err)
    }

    // Create success response
    response := Response{
        PhoneNumber: req.PhoneNumber,
        CountryCode: req.CountryCode,
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error marshaling response: %v", err)
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}