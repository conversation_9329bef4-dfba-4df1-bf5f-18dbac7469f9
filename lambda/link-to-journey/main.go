package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client

func init() {
	// Initialize the AWS SDK clients
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
}

// LinkRequest represents the request body for linking posts and thoughts to a journey
type LinkRequest struct {
	Posts            []int `json:"posts"`
	Thoughts         []int `json:"thoughts"`
	JourneyTimestamp int64 `json:"journeyTimestamp"`
}

// Response represents the API response
type Response struct {
	Message string `json:"message"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
	if tokenString == "" {
		return "", fmt.Errorf("no token provided")
	}

	// Remove "Bearer " prefix if present
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("failed to extract claims")
	}

	// Extract username from token claims
	username, ok := claims["cognito:username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token")
	}

	return username, nil
}

// Update journey with posts and thoughts
func updateJourney(ctx context.Context, username string, req LinkRequest) error {
	// Get table name from environment variable
	tableName := os.Getenv("JOURNEY_TABLE_NAME")
	if tableName == "" {
		return fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
	}

	// Convert posts and thoughts to DynamoDB number sets
	var postsSet []types.AttributeValue
	for _, post := range req.Posts {
		postsSet = append(postsSet, &types.AttributeValueMemberN{Value: strconv.Itoa(post)})
	}

	var thoughtsSet []types.AttributeValue
	for _, thought := range req.Thoughts {
		thoughtsSet = append(thoughtsSet, &types.AttributeValueMemberN{Value: strconv.Itoa(thought)})
	}

	// Build update expression and attribute values
	updateExpression := "ADD"
	expressionAttributeValues := make(map[string]types.AttributeValue)
	
	var updateParts []string
	
	if len(req.Posts) > 0 {
		updateParts = append(updateParts, "posts :posts")
		expressionAttributeValues[":posts"] = &types.AttributeValueMemberNS{Value: convertToStringSlice(req.Posts)}
	}
	
	if len(req.Thoughts) > 0 {
		updateParts = append(updateParts, "thoughts :thoughts")
		expressionAttributeValues[":thoughts"] = &types.AttributeValueMemberNS{Value: convertToStringSlice(req.Thoughts)}
	}

	if len(updateParts) == 0 {
		return fmt.Errorf("no posts or thoughts provided to link")
	}

	updateExpression += " " + updateParts[0]
	for i := 1; i < len(updateParts); i++ {
		updateExpression += ", " + updateParts[i]
	}

	// Create update input
	input := &dynamodb.UpdateItemInput{
		TableName: &tableName,
		Key: map[string]types.AttributeValue{
			"username":     &types.AttributeValueMemberS{Value: username},
			"date_created": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", req.JourneyTimestamp)},
		},
		UpdateExpression:          &updateExpression,
		ExpressionAttributeValues: expressionAttributeValues,
	}

	// Execute update
	_, err := dynamoClient.UpdateItem(ctx, input)
	if err != nil {
		return fmt.Errorf("failed to update journey: %v", err)
	}

	return nil
}

// Helper function to convert int slice to string slice
func convertToStringSlice(ints []int) []string {
	strings := make([]string, len(ints))
	for i, v := range ints {
		strings[i] = strconv.Itoa(v)
	}
	return strings
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Extract username from token
	username, err := extractUsername(request.Headers["Authorization"])
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 401,
			Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Parse request body
	var linkReq LinkRequest
	if err := json.Unmarshal([]byte(request.Body), &linkReq); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Validate request
	if linkReq.JourneyTimestamp == 0 {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error": "journeyTimestamp is required"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	if len(linkReq.Posts) == 0 && len(linkReq.Thoughts) == 0 {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error": "At least one post or thought must be provided"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Update journey with posts and thoughts
	if err := updateJourney(ctx, username, linkReq); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to link to journey: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Create success response
	response := Response{
		Message: "Successfully linked posts and thoughts to journey",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
