package main

import (
    "context"
    "encoding/json"
    "fmt"
    "math/rand"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

// Request represents the expected JSON payload
type Request struct {
    Email        string `json:"email"`
    SessionToken string `json:"sessionToken"`
}

// Response represents the Lambda response structure
type Response struct {
    Message string `json:"message,omitempty"`
}

type CheckResult struct {
    Exists bool
    Error  error
}

var (
    dynamoClient *dynamodb.Client
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    dynamoClient = dynamodb.NewFromConfig(cfg)
}

// generateVerificationCode creates a 6-digit numeric code
func generateVerificationCode() string {
    source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)

	// Generate a random number between 100000 and 999999 (inclusive).
	code := rng.Intn(900000) + 100000

	// Convert the number to a string.  The %06d format ensures leading zeros.
	return fmt.Sprintf("%06d", code)
}

func checkSessionToken(ctx context.Context, sessionToken string, results chan<- CheckResult) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        results <- CheckResult{Exists: false, Error: err}
        return
    }

    results <- CheckResult{Exists: len(result.Item) > 0, Error: nil}
}

func checkEmailExists(ctx context.Context, email string, results chan<- CheckResult) {
    input := &dynamodb.QueryInput{
        TableName: aws.String("user"),
        IndexName: aws.String("email-index"), // Assuming you have an email index
        KeyConditionExpression: aws.String("email = :email"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":email": &types.AttributeValueMemberS{Value: email},
        },
    }

    result, err := dynamoClient.Query(ctx, input)
    if err != nil {
        results <- CheckResult{Exists: false, Error: err}
        return
    }

    results <- CheckResult{Exists: result.Count > 0, Error: nil}
}

func createEmailVerification(ctx context.Context, email, sessionToken, verificationCode string) error {
    input := &dynamodb.PutItemInput{
        TableName: aws.String("email_verification_table"),
        Item: map[string]types.AttributeValue{
            "email":            &types.AttributeValueMemberS{Value: email},
            "sessionToken":     &types.AttributeValueMemberS{Value: sessionToken},
            "verificationCode": &types.AttributeValueMemberS{Value: verificationCode},
            "createdAt":        &types.AttributeValueMemberS{Value: time.Now().UTC().Format(time.RFC3339)},
        },
    }

    _, err := dynamoClient.PutItem(ctx, input)
    return err
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Validate request
    if req.Email == "" || req.SessionToken == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Email and sessionToken are required"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Create channels for results
    sessionResults := make(chan CheckResult, 1)
    emailResults := make(chan CheckResult, 1)

    // Start parallel checks
    go checkSessionToken(ctx, req.SessionToken, sessionResults)
    go checkEmailExists(ctx, req.Email, emailResults)

    // Wait for both results
    sessionResult := <-sessionResults
    emailResult := <-emailResults

    // Check for errors
    if sessionResult.Error != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking session: %v", sessionResult.Error)
    }
    if emailResult.Error != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking email: %v", emailResult.Error)
    }

    // Check session token first (401 takes precedence)
    if !sessionResult.Exists {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       `{"error": "Invalid session token"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Then check if email exists
    if emailResult.Exists {
        return events.APIGatewayProxyResponse{
            StatusCode: 409,
            Body:       `{"error": "Email already exists"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Generate verification code
    verificationCode := generateVerificationCode()

    // Create verification record
    if err := createEmailVerification(ctx, req.Email, req.SessionToken, verificationCode); err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error creating verification record: %v", err)
    }

    // Create success response
    response := Response{
        Message: "Verification code created successfully",
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error marshaling response: %v", err)
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}