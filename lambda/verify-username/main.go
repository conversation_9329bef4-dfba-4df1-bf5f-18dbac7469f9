package main

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/google/uuid"
)

// Request represents the expected JSON payload
type Request struct {
    Username     string  `json:"username"`
    SessionToken *string `json:"sessionToken,omitempty"`
}

// Response represents the Lambda response structure
type Response struct {
    IsAvailable  bool    `json:"isAvailable,omitempty"`
    Message      string  `json:"message,omitempty"`
    Username     string  `json:"username,omitempty"`
    SessionToken string  `json:"sessionToken,omitempty"`
}

type CheckResult struct {
    Exists bool
    Error  error
}

var (
	dynamoClient *dynamodb.Client
)

func init() {
	cfg, err := config.LoadDefaultConfig(context.Background())
	if err != nil {
		panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
}

func checkUserTable(ctx context.Context, username string, results chan<- CheckResult) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String("user"),
        Key: map[string]types.AttributeValue{
            "username": &types.AttributeValueMemberS{Value: username},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        results <- CheckResult{Exists: false, Error: err}
        return
    }

    results <- CheckResult{Exists: len(result.Item) > 0, Error: nil}
}

func checkTempTable(ctx context.Context, username string, results chan<- CheckResult) {
    input := &dynamodb.QueryInput{
        TableName: aws.String("user_sign_up_temp"),
        IndexName: aws.String("username-index"),
        KeyConditionExpression: aws.String("username = :username"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":username": &types.AttributeValueMemberS{Value: username},
        },
    }

    result, err := dynamoClient.Query(ctx, input)
    if err != nil {
        results <- CheckResult{Exists: false, Error: err}
        return
    }

    results <- CheckResult{Exists: result.Count > 0, Error: nil}
}

func createTempUser(ctx context.Context, username, sessionToken string) error {
    input := &dynamodb.PutItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Item: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
            "username":    &types.AttributeValueMemberS{Value: username},
        },
    }

    _, err := dynamoClient.PutItem(ctx, input)
    return err
}

func updateTempUser(ctx context.Context, username, sessionToken string) error {
    input := &dynamodb.UpdateItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
        UpdateExpression: aws.String("SET username = :username"),
        ExpressionAttributeValues: map[string]types.AttributeValue{
            ":username": &types.AttributeValueMemberS{Value: username},
        },
        ReturnValues: types.ReturnValueNone,
    }

    _, err := dynamoClient.UpdateItem(ctx, input)
    return err
}

func checkSessionTokenExists(ctx context.Context, sessionToken string) (bool, error) {
    input := &dynamodb.GetItemInput{
        TableName: aws.String("user_sign_up_temp"),
        Key: map[string]types.AttributeValue{
            "sessionToken": &types.AttributeValueMemberS{Value: sessionToken},
        },
    }

    result, err := dynamoClient.GetItem(ctx, input)
    if err != nil {
        return false, err
    }

    return len(result.Item) > 0, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Validate username
    if req.Username == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Username is required"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    // Create channels for results
    userTableResults := make(chan CheckResult, 1)
    tempTableResults := make(chan CheckResult, 1)

    // Start parallel checks
    go checkUserTable(ctx, req.Username, userTableResults)
    go checkTempTable(ctx, req.Username, tempTableResults)

    // Wait for both results
    userResult := <-userTableResults
    tempResult := <-tempTableResults

    // Check for errors
    if userResult.Error != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking user table: %v", userResult.Error)
    }
    if tempResult.Error != nil {
        return events.APIGatewayProxyResponse{}, fmt.Errorf("error checking temp table: %v", tempResult.Error)
    }

    // If username exists in either table, return 409
    if userResult.Exists || tempResult.Exists {
        response := Response{
            IsAvailable: false,
            Message:    "Username is not available",
        }
        responseBody, _ := json.Marshal(response)
        return events.APIGatewayProxyResponse{
            StatusCode: 409,
            Body:       string(responseBody),
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, nil
    }

    var sessionToken string
    var err error

    // Check if sessionToken was provided
    if req.SessionToken != nil {
        // Check if the sessionToken exists in the database
        exists, err := checkSessionTokenExists(ctx, *req.SessionToken)
        if err != nil {
            return events.APIGatewayProxyResponse{
                StatusCode: 500,
                Body:       `{"error": "Internal server error"}`,
                Headers: map[string]string{
                    "Content-Type": "application/json",
                },
            }, fmt.Errorf("error checking session token: %v", err)
        }

        if exists {
            // Update existing record
            if err := updateTempUser(ctx, req.Username, *req.SessionToken); err != nil {
                return events.APIGatewayProxyResponse{
                    StatusCode: 500,
                    Body:       `{"error": "Internal server error"}`,
                    Headers: map[string]string{
                        "Content-Type": "application/json",
                    },
                }, fmt.Errorf("error updating temp user: %v", err)
            }
            sessionToken = *req.SessionToken
        } else {
            sessionToken = ""
        }
    }

    if sessionToken == "" || req.SessionToken == nil {
        // No existing session token provided, create new one
        sessionToken = uuid.New().String()
        if err := createTempUser(ctx, req.Username, sessionToken); err != nil {
            return events.APIGatewayProxyResponse{
                StatusCode: 500,
                Body:       `{"error": "Internal server error"}`,
                Headers: map[string]string{
                    "Content-Type": "application/json",
                },
            }, fmt.Errorf("error creating temp user: %v", err)
        }
    }

    // Create success response
    response := Response{
        Username:     req.Username,
        SessionToken: sessionToken,
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Internal server error"}`,
            Headers: map[string]string{
                "Content-Type": "application/json",
            },
        }, err
    }

    // Return success response
    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}
