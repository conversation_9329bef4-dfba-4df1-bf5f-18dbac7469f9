package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client

func init() {
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		panic(fmt.Sprintf("Failed to load AWS config: %v", err))
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
}

// Request represents the incoming request to add posts and thoughts to journey
type AddToJourneyRequest struct {
	JourneyId  int64 `json:"journeyId"`
	PostIds    []int `json:"postIds"`
	ThoughtIds []int `json:"thoughtIds"`
}

// Response represents the API response
type Response struct {
	Message string `json:"message"`
}

// Extract username from JWT token
func extractUsername(authHeader string) (string, error) {
	if authHeader == "" {
		return "", fmt.Errorf("authorization header is required")
	}

	// Remove "Bearer " prefix
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == authHeader {
		return "", fmt.Errorf("invalid authorization header format")
	}

	// Parse token without verification (since we trust API Gateway's validation)
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	// Extract username from claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		if username, exists := claims["cognito:username"]; exists {
			if usernameStr, ok := username.(string); ok {
				return usernameStr, nil
			}
		}
	}

	return "", fmt.Errorf("username not found in token")
}

// Update journey with posts and thoughts
func updateJourney(ctx context.Context, username string, req AddToJourneyRequest) error {
	// Get table name from environment variable
	tableName := os.Getenv("JOURNEY_TABLE_NAME")
	if tableName == "" {
		return fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
	}

	// Build update expression and attribute values
	var updateParts []string
	expressionAttributeValues := make(map[string]types.AttributeValue)
	expressionAttributeNames := make(map[string]string)

	// Add post_ids if provided
	if len(req.PostIds) > 0 {
		postIdsJson, err := json.Marshal(req.PostIds)
		if err != nil {
			return fmt.Errorf("failed to marshal post IDs: %v", err)
		}
		updateParts = append(updateParts, "#post_ids = :post_ids")
		expressionAttributeValues[":post_ids"] = &types.AttributeValueMemberS{Value: string(postIdsJson)}
		expressionAttributeNames["#post_ids"] = "post_ids"
	}

	// Add thought_ids if provided
	if len(req.ThoughtIds) > 0 {
		thoughtIdsJson, err := json.Marshal(req.ThoughtIds)
		if err != nil {
			return fmt.Errorf("failed to marshal thought IDs: %v", err)
		}
		updateParts = append(updateParts, "#thought_ids = :thought_ids")
		expressionAttributeValues[":thought_ids"] = &types.AttributeValueMemberS{Value: string(thoughtIdsJson)}
		expressionAttributeNames["#thought_ids"] = "thought_ids"
	}

	if len(updateParts) == 0 {
		return fmt.Errorf("no posts or thoughts provided to add")
	}

	updateExpression := "SET " + strings.Join(updateParts, ", ")

	// Create update input
	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(tableName),
		Key: map[string]types.AttributeValue{
			"username":     &types.AttributeValueMemberS{Value: username},
			"date_created": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", req.JourneyId)},
		},
		UpdateExpression:          aws.String(updateExpression),
		ExpressionAttributeValues: expressionAttributeValues,
		ExpressionAttributeNames:  expressionAttributeNames,
	}

	// Execute update
	_, err := dynamoClient.UpdateItem(ctx, input)
	if err != nil {
		return fmt.Errorf("failed to update journey: %v", err)
	}

	return nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Extract username from token
	username, err := extractUsername(request.Headers["Authorization"])
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 401,
			Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Parse request body
	var addReq AddToJourneyRequest
	if err := json.Unmarshal([]byte(request.Body), &addReq); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Validate request
	if addReq.JourneyId == 0 {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error": "journeyId is required"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Both lists can be empty, so no validation needed for that

	// Update journey with posts and thoughts
	if err := updateJourney(ctx, username, addReq); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to add posts and thoughts to journey: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Create success response
	response := Response{
		Message: "Successfully added posts and thoughts to journey",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
