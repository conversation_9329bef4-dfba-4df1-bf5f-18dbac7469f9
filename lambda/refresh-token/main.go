package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
    "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
)

type Request struct {
    RefreshToken string `json:"refreshToken"`
}

type Response struct {
    IdToken     string `json:"idToken,omitempty"`
    AccessToken string `json:"accessToken,omitempty"`
    Error       string `json:"error,omitempty"`
}

var (
    cognitoClient *cognitoidentityprovider.Client
    clientId      string
)

func init() {
    cfg, err := config.LoadDefaultConfig(context.Background())
    if err != nil {
        panic(fmt.Sprintf("Failed to load AWS configuration: %v", err))
    }
    cognitoClient = cognitoidentityprovider.NewFromConfig(cfg)
    clientId = os.Getenv("CLIENT_ID")
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "Invalid request body"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    input := &cognitoidentityprovider.InitiateAuthInput{
        AuthFlow: types.AuthFlowTypeRefreshTokenAuth,
        ClientId: aws.String(clientId),
        AuthParameters: map[string]string{
            "REFRESH_TOKEN": req.RefreshToken,
        },
    }

    result, err := cognitoClient.InitiateAuth(ctx, input)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Token refresh failed: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    response := Response{
        IdToken:     *result.AuthenticationResult.IdToken,
        AccessToken: *result.AuthenticationResult.AccessToken,
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Internal server error"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers:    map[string]string{"Content-Type": "application/json"},
    }, nil
}

func main() {
    lambda.Start(handler)
}