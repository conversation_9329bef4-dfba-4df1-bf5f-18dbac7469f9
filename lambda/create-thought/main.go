package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client

func init() {
	// Initialize the AWS SDK clients
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
}

// ThoughtRequest represents the request body for creating a thought
type ThoughtRequest struct {
	ThoughtBody string `json:"thoughtBody"`
	Journey     *int   `json:"journey,omitempty"` // Optional journey ID
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Response represents the API response
type Response struct {
	DateCreated int64  `json:"dateCreated"`
	Message     string `json:"message"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
	if tokenString == "" {
		return "", fmt.Errorf("no token provided")
	}

	// Remove "Bearer " prefix if present
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("failed to extract claims")
	}

	// Extract username from token claims
	username, ok := claims["cognito:username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token")
	}

	return username, nil
}

// Validate thought request
func validateThoughtRequest(req ThoughtRequest) []ValidationError {
	var errors []ValidationError

	// Check if thoughtBody is provided
	if req.ThoughtBody == "" {
		errors = append(errors, ValidationError{
			Field:   "thoughtBody",
			Message: "thoughtBody is required",
		})
	}

	// Check thoughtBody length (max 250 characters)
	if len(req.ThoughtBody) > 250 {
		errors = append(errors, ValidationError{
			Field:   "thoughtBody",
			Message: "thoughtBody must be 250 characters or less",
		})
	}

	return errors
}

// Save thought to DynamoDB
func saveThought(ctx context.Context, username string, req ThoughtRequest) (int64, error) {
	// Get current timestamp in seconds
	dateCreated := time.Now().Unix()

	// Create item for DynamoDB
	item := map[string]types.AttributeValue{
		"username":     &types.AttributeValueMemberS{Value: username},
		"date_created": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", dateCreated)},
		"thought_body": &types.AttributeValueMemberS{Value: req.ThoughtBody},
	}

	// Add journey field if provided
	if req.Journey != nil {
		item["journey"] = &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", *req.Journey)}
	}

	// Get table name from environment variable
	tableName := os.Getenv("THOUGHT_TABLE_NAME")
	if tableName == "" {
		return 0, fmt.Errorf("THOUGHT_TABLE_NAME environment variable not set")
	}

	// Put item in DynamoDB
	_, err := dynamoClient.PutItem(ctx, &dynamodb.PutItemInput{
		TableName: &tableName,
		Item:      item,
	})

	if err != nil {
		return 0, fmt.Errorf("failed to save thought: %v", err)
	}

	return dateCreated, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Extract username from token
	username, err := extractUsername(request.Headers["Authorization"])
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 401,
			Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Parse request body
	var thoughtReq ThoughtRequest
	if err := json.Unmarshal([]byte(request.Body), &thoughtReq); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Validate request
	if validationErrors := validateThoughtRequest(thoughtReq); len(validationErrors) > 0 {
		errorsJSON, _ := json.Marshal(map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       string(errorsJSON),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Save thought to DynamoDB
	dateCreated, err := saveThought(ctx, username, thoughtReq)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to save thought: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Create success response
	response := Response{
		DateCreated: dateCreated,
		Message:     "Thought created successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 201,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
