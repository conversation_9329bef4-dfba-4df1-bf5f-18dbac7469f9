package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/golang-jwt/jwt/v5"
)

var dynamoClient *dynamodb.Client
var s3Client *s3.Client

func init() {
	// Initialize the AWS SDK clients
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}
	dynamoClient = dynamodb.NewFromConfig(cfg)
	s3Client = s3.NewFromConfig(cfg)
}

// JourneyRequest represents the request body for creating a journey
type JourneyRequest struct {
	StartDate             int64  `json:"startDate"` // milliseconds
	EndDate               int64  `json:"endDate"`   // milliseconds
	CoverPhotoResourceKey string `json:"coverPhotoResourceKey"`
	Title                 string `json:"title"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Response represents the API response
type Response struct {
	DateCreated int64  `json:"dateCreated"`
	Message     string `json:"message"`
}

// Validate journey request fields
func validateJourneyRequest(req JourneyRequest) []ValidationError {
	var errors []ValidationError

	// Validate title
	if req.Title == "" {
		errors = append(errors, ValidationError{
			Field:   "title",
			Message: "Title is required",
		})
	} else if len(req.Title) > 100 {
		errors = append(errors, ValidationError{
			Field:   "title",
			Message: "Title must not exceed 100 characters",
		})
	}

	// Validate coverPhotoResourceKey
	if req.CoverPhotoResourceKey == "" {
		errors = append(errors, ValidationError{
			Field:   "coverPhotoResourceKey",
			Message: "Cover photo resource key is required",
		})
	}

	// Validate dates
	if req.StartDate <= 0 {
		errors = append(errors, ValidationError{
			Field:   "startDate",
			Message: "Start date must be a positive number (milliseconds)",
		})
	}

	if req.EndDate <= 0 {
		errors = append(errors, ValidationError{
			Field:   "endDate",
			Message: "End date must be a positive number (milliseconds)",
		})
	}

	if req.StartDate > 0 && req.EndDate > 0 && req.StartDate >= req.EndDate {
		errors = append(errors, ValidationError{
			Field:   "endDate",
			Message: "End date must be after start date",
		})
	}

	return errors
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
	if tokenString == "" {
		return "", fmt.Errorf("no token provided")
	}

	// Remove "Bearer " prefix if present
	if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
		tokenString = tokenString[7:]
	}

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("failed to extract claims")
	}

	// Extract username from token claims
	username, ok := claims["cognito:username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token")
	}

	return username, nil
}

// Check if an object exists in S3
func objectExists(ctx context.Context, bucketName, key string) (bool, error) {
	_, err := s3Client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: &bucketName,
		Key:    &key,
	})

	if err != nil {
		// Check if the error is because the object doesn't exist
		if err.Error() == "NotFound" {
			return false, nil
		}
		return false, fmt.Errorf("error checking if object exists: %v", err)
	}

	return true, nil
}

// Verify that the cover photo resource exists in S3
func verifyCoverPhotoResource(ctx context.Context, bucketName, resourceKey string) error {
	exists, err := objectExists(ctx, bucketName, resourceKey)
	if err != nil {
		return fmt.Errorf("error verifying cover photo resource: %v", err)
	}

	if !exists {
		return fmt.Errorf("cover photo resource with key '%s' does not exist in bucket '%s'",
			resourceKey, bucketName)
	}

	return nil
}

// Get user profile picture from user table
func getUserProfilePicture(ctx context.Context, username string) (string, error) {
	userTableName := os.Getenv("USER_TABLE_NAME")
	if userTableName == "" {
		return "", fmt.Errorf("USER_TABLE_NAME environment variable not set")
	}

	// Get user item from DynamoDB
	result, err := dynamoClient.GetItem(ctx, &dynamodb.GetItemInput{
		TableName: &userTableName,
		Key: map[string]types.AttributeValue{
			"username": &types.AttributeValueMemberS{Value: username},
		},
	})

	if err != nil {
		return "", fmt.Errorf("failed to get user profile: %v", err)
	}

	// Extract profilePicture attribute if it exists
	if result.Item != nil {
		if val, ok := result.Item["profilePicture"].(*types.AttributeValueMemberS); ok {
			return val.Value, nil
		}
	}

	// Return empty string if no profile picture found
	return "", nil
}

// Save journey to DynamoDB
func saveJourney(ctx context.Context, username string, req JourneyRequest, userProfilePictureResourceKey string) (int64, error) {
	// Get current timestamp in seconds
	dateCreated := time.Now().Unix()

	// Create item for DynamoDB
	item := map[string]types.AttributeValue{
		"username":                 &types.AttributeValueMemberS{Value: username},
		"date_created":             &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", dateCreated)},
		"title":                    &types.AttributeValueMemberS{Value: req.Title},
		"start_date":               &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", req.StartDate)},
		"end_date":                 &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", req.EndDate)},
		"cover_photo_resource_key": &types.AttributeValueMemberS{Value: req.CoverPhotoResourceKey},
	}

	// Add user profile picture resource key if it exists
	if userProfilePictureResourceKey != "" {
		item["user_profile_picture_resource_key"] = &types.AttributeValueMemberS{Value: userProfilePictureResourceKey}
	}

	// Get table name from environment variable
	tableName := os.Getenv("JOURNEY_TABLE_NAME")
	if tableName == "" {
		return 0, fmt.Errorf("JOURNEY_TABLE_NAME environment variable not set")
	}

	// Put item in DynamoDB
	_, err := dynamoClient.PutItem(ctx, &dynamodb.PutItemInput{
		TableName: &tableName,
		Item:      item,
	})

	if err != nil {
		return 0, fmt.Errorf("failed to save journey: %v", err)
	}

	return dateCreated, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Extract username from token
	username, err := extractUsername(request.Headers["Authorization"])
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 401,
			Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Parse request body
	var journeyReq JourneyRequest
	if err := json.Unmarshal([]byte(request.Body), &journeyReq); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Log the journey request for debugging
	log.Printf("Received journey request from user %s: %+v", username, journeyReq)

	// Validate journey request fields
	if validationErrors := validateJourneyRequest(journeyReq); len(validationErrors) > 0 {
		errorResponse, _ := json.Marshal(map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       string(errorResponse),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Get bucket name from environment variable
	bucketName := os.Getenv("JOURNEY_COVER_PHOTOS_BUCKET")
	if bucketName == "" {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "JOURNEY_COVER_PHOTOS_BUCKET environment variable not set"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Verify that the cover photo resource exists in S3
	if err := verifyCoverPhotoResource(ctx, bucketName, journeyReq.CoverPhotoResourceKey); err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error": "Cover photo verification failed: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Get user profile picture
	userProfilePictureResourceKey, err := getUserProfilePicture(ctx, username)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to get user profile: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Save journey to DynamoDB
	dateCreated, err := saveJourney(ctx, username, journeyReq, userProfilePictureResourceKey)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error": "Failed to save journey: %v"}`, err),
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	// Create success response
	response := Response{
		DateCreated: dateCreated,
		Message:     "Journey created successfully",
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       `{"error": "Failed to create response"}`,
			Headers:    map[string]string{"Content-Type": "application/json"},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 201, // Created
		Body:       string(responseBody),
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}, nil
}

func main() {
	lambda.Start(handler)
}
