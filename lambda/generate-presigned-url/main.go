package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "os"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/s3"
    "github.com/golang-jwt/jwt/v5"
)

var s3Client *s3.Client

func init() {
    // Initialize the S3 client
    cfg, err := config.LoadDefaultConfig(context.TODO())
    if err != nil {
        log.Fatalf("unable to load SDK config, %v", err)
    }
    s3Client = s3.NewFromConfig(cfg)
}

// Request represents the API request parameters
type Request struct {
    PostKey     string `json:"postKey"`
    MomentsSize int    `json:"momentsSize"`
}

// Response represents the API response
type Response struct {
    PreSignedUrls []string `json:"preSignedUrls"`
    Keys         []string `json:"keys"`
}

// Extract username from JWT token
func extractUsername(tokenString string) (string, error) {
    if tokenString == "" {
        return "", fmt.Errorf("no token provided")
    }

    // Remove "Bearer " prefix if present
    if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
        tokenString = tokenString[7:]
    }

    token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
    if err != nil {
        return "", fmt.Errorf("failed to parse token: %v", err)
    }

    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
        return "", fmt.Errorf("failed to extract claims")
    }

    // Extract username from token claims
    username, ok := claims["cognito:username"].(string)
    if !ok {
        return "", fmt.Errorf("username not found in token")
    }

    return username, nil
}

// Generate a presigned URL for uploading a photo
func generatePresignedUrl(ctx context.Context, bucket, key string) (string, error) {
    presignClient := s3.NewPresignClient(s3Client)
    
    // Create presigned PUT URL with 15-minute expiration
    presignedReq, err := presignClient.PresignPutObject(ctx, &s3.PutObjectInput{
        Bucket: &bucket,
        Key:    &key,
    }, func(opts *s3.PresignOptions) {
        opts.Expires = 15 * time.Minute
    })
    
    if err != nil {
        return "", fmt.Errorf("failed to create presigned URL: %v", err)
    }
    
    return presignedReq.URL, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // Extract username from token
    username, err := extractUsername(request.Headers["Authorization"])
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 401,
            Body:       fmt.Sprintf(`{"error": "Invalid token: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Get bucket name from environment variable
    bucketName := os.Getenv("JOURNEY_PHOTOS_BUCKET")
    if bucketName == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       `{"error": "Bucket name not configured"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Parse request body
    var req Request
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       fmt.Sprintf(`{"error": "Invalid request body: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Validate request parameters
    if req.PostKey == "" {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "postKey is required"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    if req.MomentsSize <= 0 || req.MomentsSize > 10 {
        return events.APIGatewayProxyResponse{
            StatusCode: 400,
            Body:       `{"error": "momentsSize must be between 1 and 10"}`,
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    // Generate presigned URLs for each moment
    uploadURLs := make([]string, req.MomentsSize)
    keys := make([]string, req.MomentsSize)

    for i := 0; i < req.MomentsSize; i++ {
        // Generate a unique key for each moment
        key := fmt.Sprintf("journeys/%s/%s/%d", username, req.PostKey, i)
        keys[i] = key

        // Generate presigned URL
        presignedUrl, err := generatePresignedUrl(ctx, bucketName, key)
        if err != nil {
            return events.APIGatewayProxyResponse{
                StatusCode: 500,
                Body:       fmt.Sprintf(`{"error": "Failed to generate presigned URL: %v"}`, err),
                Headers:    map[string]string{"Content-Type": "application/json"},
            }, nil
        }
        uploadURLs[i] = presignedUrl
    }

    // Create response
    response := Response{
        PreSignedUrls: uploadURLs,
        Keys:       keys,
    }

    // Convert response to JSON
    responseBody, err := json.Marshal(response)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       fmt.Sprintf(`{"error": "Failed to create response: %v"}`, err),
            Headers:    map[string]string{"Content-Type": "application/json"},
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Content-Type": "application/json",
        },
    }, nil
}

func main() {
    lambda.Start(handler)
}
