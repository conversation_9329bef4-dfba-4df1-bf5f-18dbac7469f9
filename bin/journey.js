#!/usr/bin/env node

const { App, Stack } = require('aws-cdk-lib');
const { StorageStack } = require('../lib/storage-stack');
const { UserPoolStack } = require('../lib/user-pool-stack');
const { IdentityPoolStack } = require('../lib/identity-pools/identity-pool-stack');
const { SignUpStack } = require('../lib/sign-up/sign-up-stack');
const { SignUpAPIStack } = require('../lib/sign-up/sign-up-api-stack');
const { ProfileStack } = require('../lib/profile/profile-stack');
const { LoginStack } = require('../lib/login/login-stack');
const { AuthStack } = require('../lib/auth/auth-stack');
const { CreatePostStack } = require('../lib/post/create-post-stack');
const { CreateJourneyStack } = require('../lib/journey/create-journey-stack');

class JourneyStack extends Stack {
  constructor(scope, id, props) {
    super(scope, id, props);

    // ----------------------TOP LEVEL STACKS----------------------
    // ----------------------NO DEPENDENCIES-----------------------
    // Create user pool
    const userPoolStack = new UserPoolStack(this, 'UserPoolStack', {
      env: props.env,
    });

    // Create storage stack
    const storageStack = new StorageStack(this, 'StorageStack', {
      env: props.env,
      isProd: process.env.NODE_ENV === 'production',
    });
    // ----------------------END TOP LEVEL STACKS------------------

    // Create ProfileStack first without passing identityPool
    const profileStack = new ProfileStack(this, 'ProfileStack', {
      env: props.env,
      userTable: storageStack.userTable,
      userPool: userPoolStack.userPool,
      // Remove identityPool from here
      isProd: process.env.NODE_ENV === 'production',
    });
    
    profileStack.addDependency(storageStack);
    profileStack.addDependency(userPoolStack);

    // Now create IdentityPoolStack with the profilePicturesBucket
    const identityPoolStack = new IdentityPoolStack(this, 'IdentityPoolStack', {
      env: props.env,
      userPool: userPoolStack.userPool,
      userPoolClient: userPoolStack.userPoolClient,
    });

    identityPoolStack.addDependency(userPoolStack);

    const signUpStack = new SignUpStack(this, 'SignUpStack', {
      env: props.env,
      userPool: userPoolStack.userPool,
      userPoolClient: userPoolStack.userPoolClient,
      userTable: storageStack.userTable,
    });

    signUpStack.addDependency(userPoolStack);
    signUpStack.addDependency(storageStack);

    const signUpAPIStack = new SignUpAPIStack(this, 'SignUpAPIStack', {
      env: props.env,
      verifyUsername: signUpStack.verifyUsername,
      verifyPhone: signUpStack.verifyPhone,
      verifyEmail: signUpStack.verifyEmail,
      validateEmailVerificationCode: signUpStack.validateEmailVerificationCode,
      validateSMSVerificationCode: signUpStack.validateSMSVerificationCode,
      removeEmail: signUpStack.removeEmail,
      removePhoneNumber: signUpStack.removePhoneNumber,
      signUp: signUpStack.signUp,
      destroySession: signUpStack.destroySession,
    });

    signUpAPIStack.addDependency(signUpStack);

    const loginStack = new LoginStack(this, 'LoginStack', {
      env: props.env,
      userPool: userPoolStack.userPool,
      userPoolClient: userPoolStack.userPoolClient,
    });

    loginStack.addDependency(userPoolStack);

    const authStack = new AuthStack(this, 'AuthStack', {
      env: props.env,
      userPool: userPoolStack.userPool,
      userPoolClient: userPoolStack.userPoolClient,
    });

    authStack.addDependency(userPoolStack);

    const createPostStack = new CreatePostStack(this, 'CreatePostStack', {
      env: props.env,
      userPool: userPoolStack.userPool,
      isProd: process.env.NODE_ENV === 'production',
      profilePicturesBucket: profileStack.profilePicturesBucket,
    });

    createPostStack.addDependency(userPoolStack);
    createPostStack.addDependency(profileStack);

    const createJourneyStack = new CreateJourneyStack(this, 'CreateJourneyStack', {
      env: props.env,
      userPool: userPoolStack.userPool,
      isProd: process.env.NODE_ENV === 'production',
    });

    createJourneyStack.addDependency(userPoolStack);
  }
}

const app = new App();

new JourneyStack(app, 'JourneyStack', {
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION,
  },
});

app.synth();
